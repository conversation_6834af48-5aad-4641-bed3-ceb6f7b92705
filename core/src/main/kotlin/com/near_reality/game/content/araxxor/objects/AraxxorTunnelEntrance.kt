package com.near_reality.game.content.araxxor.objects

import com.near_reality.game.content.araxxor.AraxxorInstance
import com.zenyte.game.task.WorldTasksManager.schedule
import com.zenyte.game.util.Direction
import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.cutscene.FadeScreen
import com.zenyte.game.world.`object`.ObjectAction
import com.zenyte.game.world.`object`.ObjectId.WEB_TUNNEL_ARAXXOR
import com.zenyte.game.world.`object`.WorldObject
import com.zenyte.plugins.dialogue.PlainChat

/**
 * <AUTHOR> | Glabay-Studios
 * <AUTHOR> by <PERSON> (Discord: imslickk)
 */
class AraxxorTunnelEntrance : ObjectAction {


    override fun handleObjectAction(player: Player?, `object`: WorldObject?, name: String?, optionId: Int, option: String?) {
        player ?: return
        `object` ?: return
        if (`object`.id == WEB_TUNNEL_ARAXXOR) //Entrance Tunnel
            AraxxorInstance(player).constructRegion()
        else if (`object`.id == 54274) { //Exit Tunnel
            val exitLocation = Location(3657, 3407, 0)
            player.lock()
            val screen = FadeScreen(player) {
                AraxxorInstance(player).destroyRegion()
                player.setLocation(exitLocation)
                player.faceDirection(Direction.EAST)
                player.dialogueManager.start(PlainChat(player, "You escape through the webbed tunnel."))
            }
            player.dialogueManager.start(PlainChat(player, "You escape through the webbed tunnel.", false))
            screen.fade()
            schedule(2) {
                screen.unfade()
                player.unlock()
            }
        }

    }

    override fun getObjects(): Array<Any> = arrayOf(WEB_TUNNEL_ARAXXOR, 54274)
}