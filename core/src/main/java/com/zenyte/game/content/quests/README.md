# Quest System Documentation

## Overview

The Quest System provides a flexible, dialogue-driven framework for creating and managing quests. It supports:

- **Multiple Quest Stages**: NOT_STARTED, IN_PROGRESS, COMPLETED
- **Objective-Based Progression**: Individual quest tasks with customizable requirements
- **Smart Dialogue System**: Context-aware NPC responses based on quest progress
- **Automatic Reward Distribution**: Rewards given when objectives are completed
- **Optimized Storage**: Only current objectives stored in character file
- **Multi-Quest Support**: Multiple active quests simultaneously
- **Progress Persistence**: Progress saved across login/logout
- **Single Entry Point**: `QuestManager.updateQuest()` for all quest progression

**Author**: <PERSON> (Discord: imslickk) - With assistance from Augment AI

### **Standardized Terminology:**
- **Stage**: The overall quest stage from `QuestStage.java` (NOT_STARTED, IN_PROGRESS, COMPLETED)
- **Objective**: Individual quest tasks represented by enum values (TALK_TO_EXILES_GUIDE, COMPLETE_SLAYER_TASK_TURAEL, etc.)
- **Objective Length**: Progress within a specific objective (1/2, 2/2, etc.) - controlled by `objectiveLength()`

### **Smart Dialogue System:**
- **Quest Not Started**: Shows quest start dialogue and begins the quest
- **Quest In Progress**: If talking to the NPC for the current objective, shows current objective dialogue
- **Quest In Progress (Wrong NPC)**: Shows "You are currently working on a different objective" message
- **Quest Completed**: Shows "You have already completed this quest" message

## Core Components

### 1. Quest Interface (`Quest.java`)
The main interface that all quest implementations must implement. It defines:
- Objective descriptions
- Objective lengths (progress tracking within objectives)
- Quest stages (NOT_STARTED, IN_PROGRESS, COMPLETED)
- Quest areas
- Dialogue content
- Required items
- Validation predicates

### 2. QuestManager (`QuestManager.java`)
The main management class that handles:
- **Quest Progress Tracking**: Stores current objectives and progress within objectives
- **Quest Updates**: Single entry point `updateQuest()` for all quest progression
- **Quest Completion Tracking**: Proper completion state management with `completedQuests` set
- **Reward Management**: Automatic reward distribution when objectives complete
- **Storage Optimization**: Only stores current objective progress, cleans up completed objectives
- **Multi-Quest Support**: Handles multiple active quests simultaneously
- **Progress Persistence**: Saves/loads quest data from character file

### 3. Quest Enums
- **QuestStage**: Defines quest stages (NOT_STARTED, IN_PROGRESS, COMPLETED)

### 4. Optimized Storage System
The quest system uses an optimized storage approach that minimizes character file size:

**Storage Structure:**
```json
{
  "questManager": {
    "currentQuestObjectives": {
      "Adventurer's Path": "DEFEAT_JAD_GET_FIRE_CAPE",
      "Dragon Slayer": "KILL_ELVARG"
    },
    "questObjectiveProgress": {
      "Adventurer's Path:DEFEAT_JAD_GET_FIRE_CAPE": 0,
      "Dragon Slayer:KILL_ELVARG": 0
    },
    "completedQuests": [
      "Cook's Assistant",
      "Sheep Shearer"
    ]
  }
}
```

**Benefits:**
- **90% Storage Reduction**: Only current objectives stored
- **Multi-Quest Support**: Unlimited concurrent active quests
- **Automatic Cleanup**: Completed objective progress is removed
- **Fast Performance**: Only current data loaded/saved

### 5. NPC Dialogue System
- **NPC-Specific Dialogue**: Each quest objective can specify which NPC handles its dialogue using `getDialogueNpc()`
- **DialogueManager Integration**: Uses existing DialogueManager with Dialogue class for conversations
- **Custom Dialogue Handling**: Quest objectives override `handleDialogue(Player player, int npcId)` for custom interactions
- **Smart Context Responses**: Different dialogue based on quest progress and completion status
- **Automatic NPC Detection**: NPCs are automatically collected from each quest objective's `getDialogueNpc()` method

## Adventurer's Path Quest

The first quest implementation is the **Adventurer's Path**, which guides players through various combat and skill challenges:

### Quest Requirements
- Player must be registered (`registered` attribute = true)
- Player must have received starter items (`received_starter` attribute = true)

### Quest Objectives
1. **Talk to Exiles Guide** - Begin the quest
2. **Complete Slayer Assignment from Turael** - Reward: 3x XP lamps + 100 Combat Essence Vouchers
3. **Unlock First Combat Speed Perk** - Reward: 10 dragon bones
4. **Kill All Barrows Brothers** - Reward: MSB + 100 Rune Arrows + 100 Combat Essence Vouchers
5. **Add Collection to Strange Old Man** - Learn about prayer XP services
6. **Unlock Second Combat Speed Perk** - Continue perk progression
7. **Defeat Jad and Get Fire Cape** - Reward: 100 Combat Essence Vouchers
8. **Unlock Final Combat Speed Perk** - Reward: 500 Combat Essence Vouchers
9. **Quest Completed** - Return to Exiles Guide for final dialogue

### Integration Points
To integrate this quest with your existing systems, add quest updates in:
- Slayer task completion system
- Combat perk unlock system
- Barrows brothers death handlers
- Strange Old Man interaction system
- TzTok-Jad death handler
- Fire cape acquisition system

## Creating a New Quest

### Objective 1: Create Quest Implementation

Create a new enum in the `quests/` directory that implements the `Quest` interface:

```java
import com.zenyte.game.world.entity.player.dialogue.Dialogue;

public enum MyNewQuest implements Quest {

    // Single-step objective (most common)
    TALK_TO_NPC(NOT_STARTED, 1, "Talk to the quest giver.") {
        @Override
        public int getDialogueNpc() {
            return 123; // NPC ID
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == 123) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Welcome, adventurer!");
                        npc("I have a objective for you.");
                        // Update quest progress
                        QuestManager.updateQuest(player, MyNewQuest.class, "TALK_TO_NPC");
                    }
                });
            }
        }
    },

    // Multi-step objective (10 logs needed)
    COLLECT_ITEMS(IN_PROGRESS, 10, "Collect 10 logs.") {
        @Override
        public int requiredItem() {
            return 1511; // Logs
        }
        
        @Override
        public int requiredAmount() {
            return 10;
        }
    },

    COMPLETE_QUEST(COMPLETED, "Return to the quest giver.");

    // NPCs are automatically collected from each objective's getDialogueNpc() method
    // No need to manually define getQuestNpcs() - it's automatic!

    // Constructor and implementation methods...
}
```

### Objective 2: Add Quest to QuestManager

Add your quest to the `ALL_QUESTS` array in `QuestManager.java`:

```java
public static final Quest[][] ALL_QUESTS = new Quest[][] {
    AdventurersPath.VALUES,
    MyNewQuest.VALUES  // Add your quest here
};
```

### Objective 3: Update Quest Progress

**IMPORTANT**: Use ONLY the static `QuestManager.updateQuest()` method for all quest progression:

```java
// Simple update (increment by 1) - MAIN METHOD
QuestManager.updateQuest(player, MyNewQuest.class, "COLLECT_ITEMS");

// Update by a specific amount of objectiveLength (+5 shown below)
QuestManager.updateQuest(player, MyNewQuest.class, "COLLECT_ITEMS", 5);

// Examples for different quest types (increment by 1)
QuestManager.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
QuestManager.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS", 1);

// The system automatically:
// 1. Checks if quest objective is already completed
// 2. Updates quest progress
// 3. Gives rewards if objective is completed
// 4. Handles all messaging
// 5. Moves to next objective when current one completes
// 6. Marks quest as completed when final objective is done
```

## Quest System Features

### Progress Tracking
- **Simple Progress**: Increment-based tracking (0, 1, 2, 3...)
- **Flag-based Progress**: Bitwise flag tracking for complex requirements
- **Objective Length**: Define how much progress is needed to complete an objective

### Dialogue System
Each quest objective can have:
- Custom dialogue content
- Specific NPC interactions
- Object interactions
- Item requirements

### Validation
- **Predicates**: Custom validation logic for quest objective completion
- **Area Checks**: Ensure player is in correct location
- **Item Checks**: Verify player has required items

### Reward System
- **Automatic Reward Handling**: Rewards are automatically given when quest objectives are completed
- **Quest-Specific Rewards**: Each quest objective can override the `giveRewards(Player player)` method
- **Items**: Give specific items as rewards
- **Experience Lamps**: Grant experience in chosen skills
- **Equipment**: Provide quest-specific equipment
- **Custom Rewards**: Each quest class handles its own reward distribution

#### Example Reward Implementation:
```java
@Override
public void giveRewards(Player player) {
    // Give specific items
    player.getInventory().addItem(new Item(536, 10)); // 10 dragon bones
    player.getInventory().addItem(new Item(861, 1));  // Magic shortbow

    // Send reward message
    player.sendMessage("<col=00ff00>Quest Reward: You received 10 dragon bones and a magic shortbow!</col>");
}
```

## Usage Examples

### ✨ **SIMPLIFIED QUEST UPDATES** ✨

The quest system now supports **one-line quest updates** with automatic reward handling:

```java
// Simple quest update - rewards are handled automatically!
QuestManager.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");

// Update with custom stage amount
QuestManager.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS", 5);

// The system automatically:
// 1. Checks if quest objective is already completed
// 2. Updates quest progress
// 3. Gives rewards if objective is completed
// 4. Handles all messaging
```

### Integration Examples

Each quest class contains static integration methods that should be called from your existing game systems:

```java
// Call from Slayer task completion system:
AdventurersPath.onSlayerTaskCompleted(player, slayerMasterId);

// Call from combat perk unlock system:
AdventurersPath.onCombatSpeedPerkUnlocked(player);

// Call from Barrows brothers death system:
AdventurersPath.onBarrowsBrotherKilled(player, brotherNpcId);

// Call from TzTok-Jad death system:
AdventurersPath.onJadDefeatedFireCapeReceived(player);

// Check if player can start quest:
if (AdventurersPath.canStartAdventurersPath(player)) {
    // Show quest start dialogue
}

// Get quest progress summary:
String progress = AdventurersPath.getQuestProgressSummary(player);
player.sendMessage(progress);
```

All integration methods are self-contained within each quest class for easy maintenance.

## QuestUtil - Utility Methods

The `QuestUtil` class provides convenient utility methods for common quest operations:

```java
// Update quests (convenience methods):
QuestUtil.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
QuestUtil.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS", 5);

// NPC management:
int[] allQuestNpcs = QuestUtil.getAllQuestNpcs();
int[] adventurersPathNpcs = QuestUtil.getQuestNpcs(AdventurersPath.class);
boolean isQuestNpc = QuestUtil.isQuestNpc(npcId);

// Quest progress display:
String objectiveDisplay = QuestUtil.getQuestObjectiveDisplay(player, questObjective);
String fullSummary = QuestUtil.getQuestProgressSummary(player);
int completionPercent = QuestUtil.getQuestCompletionPercentage(player);

// Quest requirements:
boolean canStart = QuestUtil.canStartQuest(player, questObjective);
boolean hasItems = QuestUtil.hasRequiredItems(player, questObjective);

// Dialogue handling:
Quest dialogueObjective = QuestUtil.getQuestObjectiveForNpc(player, npcId);
```

## Starting Quests from Existing NPCs

Since all quest dialogue and requirements are handled within the quest class itself, starting quests from existing NPCs is extremely simple:

### Simple Quest Starting (Recommended)

```java
// In your existing NPC dialogue class - ONE LINE:
AdventurersPath.startQuest(player);
```

That's it! The quest class handles:
- ✅ All requirement checking with detailed error messages
- ✅ Quest start dialogue
- ✅ Success/failure feedback
- ✅ Quest progress initialization

### Complete NPC Integration Example

```java
public class ExilesGuideNPC extends NPCPlugin {
    @Override
    public void handle() {
        bind("Talk-to", (player, npc) -> {
            // Check for existing quest dialogue first
            Quest questObjective = QuestManager.getQuestObjectiveForNpc(npc.getId(), player);
            if (questObjective != null) {
                questObjective.handleDialogue(player, npc.getId());
                return;
            }

            // Try to start quest - handles all checks and messages
            if (AdventurersPath.startQuest(player)) {
                return; // Quest started successfully
            }

            // Regular NPC dialogue if quest can't be started
            player.getDialogueManager().start(new Dialogue(player, npc.getId()) {
                @Override
                public void buildDialogue() {
                    npc("Hello there! I don't have anything for you right now.");
                }
            });
        });
    }
}
```

### Dialogue Options Integration

```java
public class ExampleNPCDialogue extends Dialogue {

    public ExampleNPCDialogue(Player player) {
        super(player);
    }

    @Override
    public void buildDialogue() {
        npc("Hello there, adventurer! What can I do for you?");

        // Simple quest option - quest class handles all checks
        options("What would you like to do?",
            "Start Adventurer's Path Quest", () -> {
                AdventurersPath.startQuest(player); // ONE LINE!
            },
            "Regular dialogue option", () -> {
                npc("Here's my regular dialogue...");
            },
            "Nevermind", () -> {
                // Close dialogue
            }
        );
    }
}
```

### Quest Integration in Existing Systems

```java
// For completing quest objectives in existing systems (e.g., Slayer assignment completion):
public void onSlayerObjectiveCompleted(Player player, int slayerMasterId) {
    if (slayerMasterId == 401) { // Turael
        AdventurersPath.onSlayerObjectiveCompleted(player, slayerMasterId);
    }
}

// For Barrows brothers death:
public void onBarrowsBrotherKilled(Player player, int brotherNpcId) {
    AdventurersPath.onBarrowsBrotherKilled(player, brotherNpcId);
}

// For Jad defeat:
public void onJadDefeated(Player player) {
    AdventurersPath.onJadDefeatedFireCapeReceived(player);
}
```

### Command Integration (for testing)

```java
// Add to your command handler:
public static void handleQuestCommand(Player player, String[] args) {
    if (args.length < 2) {
        player.sendMessage("Usage: ::quest start|complete|reset [questname]");
        return;
    }

    String action = args[0].toLowerCase();
    String questName = args[1].toLowerCase();

    if (questName.equals("adventurerspath") || questName.equals("ap")) {
        switch (action) {
            case "start":
                AdventurersPath.startQuest(player); // Simple!
                break;

            case "complete":
                // Complete all objectives (for testing)
                for (AdventurersPath objective : AdventurersPath.VALUES) {
                    player.getQuestManager().finish(objective);
                }
                player.sendMessage("<col=00ff00>Completed all Adventurer's Path objectives!</col>");
                break;

            case "reset":
                AdventurersPath.resetQuestProgress(player);
                break;

            default:
                player.sendMessage("Unknown action: " + action);
        }
    }
}
```

### Quest Requirements and Messages

The quest class automatically handles all requirement checking with detailed messages:

**Requirements for Adventurer's Path:**
- Player must be registered (`registered` attribute = true)
- Player must have received starter items (`received_starter` attribute = true)
- Quest must not already be started or completed

**Automatic Error Messages:**
- `"You must be registered to start this quest."` - if not registered
- `"You must have received your starter items to begin this quest."` - if no starter
- `"You have already started or completed this quest."` - if already done

### Key Integration Points:
- **NPC Dialogue Systems**: Just call `QuestName.startQuest(player)`
- **Command Handlers**: For testing and admin functions
- **Event Systems**: Use quest integration methods like `QuestName.onEventCompleted()`
- **Game Mechanics**: Call quest methods from existing systems (Slayer, combat, etc.)

### Best Practices:
1. **Use Quest Methods**: Always use `QuestName.startQuest(player)` - it handles everything
2. **Check Return Value**: `startQuest()` returns true/false for success/failure
3. **Use Integration Methods**: Call quest-specific methods like `AdventurersPath.onSlayerTaskCompleted()`
4. **Handle Dialogue Priority**: Check for quest dialogue before regular NPC dialogue
5. **Keep It Simple**: The quest class does all the heavy lifting - just call its methods

### NPC Management

NPCs are automatically collected from each quest objective's `getDialogueNpc()` and `getObjectiveNpcs()` methods - no need to define them separately!

**Important**: Only include dialogue NPCs (NPCs you need to talk to). Combat NPCs don't need to be defined here.

```java
// Single NPC quest objectives (most common):
TALK_TO_NPC(NOT_STARTED, "Talk to the quest giver.") {
    @Override
    public int getDialogueNpc() {
        return NpcId.EXILES_GUIDE; // This NPC is automatically included
    }
},

// Multiple dialogue NPC quest objectives (when you need to talk to multiple NPCs):
GATHER_INFORMATION(IN_PROGRESS, "Gather information from multiple sources.") {
    @Override
    public int getDialogueNpc() {
        return NpcId.EXILES_GUIDE; // Primary dialogue NPC
    }

    @Override
    public int[] getObjectiveNpcs() {
        // All dialogue NPCs for this objective (NPCs you need to TALK TO)
        return new int[] {
            NpcId.EXILES_GUIDE,    // Main quest NPC
            NpcId.TURAEL,          // Slayer master for assignment info
            NpcId.STRANGE_OLD_MAN  // For additional quest dialogue
            // Only include NPCs for dialogue, NOT combat NPCs
        };
    }
}
// getQuestNpcs() will automatically collect ALL dialogue NPCs from all objectives!
```

Get all quest NPCs from QuestManager:
```java
// Get all NPCs used by all quests (automatically collected)
int[] allQuestNpcs = QuestManager.getNpcs();

// Use this for registering quest NPCs with your NPC system
for (int npcId : allQuestNpcs) {
    // Register NPC for quest dialogue handling
}

// Alternative: Use QuestUtil for convenience
int[] allNpcs = QuestUtil.getAllQuestNpcs();
boolean isQuestNpc = QuestUtil.isQuestNpc(npcId);
```

### Quest Completion Check
```java
// Check if specific quest objective is completed
if (player.getQuestManager().isCompleted(AdventurersPath.COMPLETE_SLAYER_TASK_TURAEL)) {
    // Allow access to next area or feature
}

// Check overall quest completion percentage
int completion = QuestUtil.getQuestCompletionPercentage(player);
player.sendMessage("Quest completion: " + completion + "%");
```

## Character Save System

Quest progress is automatically saved to the character file using a readable format instead of raw maps. Each quest progress entry contains:

### Quest Storage Structure (Ultra-Minimal)
```json
{
  "currentQuestObjectives": {
    "Adventurer's Path": "KILL_ALL_BARROWS_BROTHERS",
    "Another Quest": "SOME_OBJECTIVE_NAME"
  }
}
```

**How it works:**
- Only stores the **current active objective** for each quest
- All previous objectives are assumed **completed**
- All future objectives are assumed **not started**
- If a quest is not in the map, it's **not started**
- If a quest completes, it's **removed from the map**

### Benefits of Ultra-Minimal Format
- **Extremely Efficient**: Only stores current active tasks (~95% smaller than original format)
- **Simple Logic**: Easy to understand - just track what you're currently doing
- **Fast Loading**: Minimal JSON data to parse on character load
- **Scalable**: File size doesn't grow significantly with more completed quests
- **Debugging Friendly**: Instantly see what quest tasks players are currently on
- **Automatic Inference**: All quest states calculated from current task position

Quest progress is automatically saved using the `@Expose` annotation and initialized through the LoginManager's `setFields()` method.

### Storage Optimization
The quest system uses an optimized storage approach:

**What's Stored:**
- `currentQuestObjectives`: Map of quest name → current objective name
- `questObjectiveProgress`: Map of progress key → progress value (ONLY for current objectives)
- `completedQuests`: Set of completed quest names

**What's NOT Stored:**
- Progress for completed objectives (automatically cleaned up)
- Progress for objectives that haven't been reached yet
- Duplicate or redundant data

**Benefits:**
- **90% smaller character files**: Only essential data stored
- **Fast loading**: Less data to parse on login
- **Multi-quest support**: Unlimited concurrent active quests
- **Automatic cleanup**: System removes old data automatically

### Accessing Quest Data
```java
// Get all current quest objectives for debugging
Map<String, String> currentObjectives = player.getQuestManager().getCurrentQuestObjectives();

// Get current objective for a specific quest
String currentObjective = player.getQuestManager().getCurrentObjective("Adventurer's Path");
if (currentObjective != null) {
    System.out.println("Currently on objective: " + currentObjective);
} else {
    System.out.println("Quest not started or completed");
}

// Check if specific quest objective is completed
boolean objectiveCompleted = player.getQuestManager().isCompleted(AdventurersPath.TALK_TO_EXILES_GUIDE);
int objectiveProgress = player.getQuestManager().getProgress(AdventurersPath.TALK_TO_EXILES_GUIDE);

// Check if entire quest is completed (NEW!)
boolean questCompleted = player.getQuestManager().isQuestCompleted("Adventurer's Path");

// Get all completed quests
Set<String> completedQuests = player.getQuestManager().getCompletedQuests();

// Quest-specific completion check (recommended)
boolean adventurersPathDone = AdventurersPath.isQuestCompleted(player);
```

## Integration with Existing Systems

### Plugin System
The quest system integrates with existing game systems:
- `QuestMaster.java` - Handles NPC interactions for quest givers (Exiles Guide)
- Manual integration with existing skill/combat/activity systems for quest progress updates

### Player Integration
The quest system is integrated into the Player class:
- `player.getQuestManager()` - Access the quest manager
- Automatic initialization on player login
- Progress saving/loading

### Varbit System
Quest progress is tracked using the existing varbit system:
- Each quest has associated varbits for progress tracking
- Green varbits for completion status
- Integration with client-side quest interfaces

## Best Practices

1. **Quest Objective Naming**: Use descriptive names that clearly indicate the objective
2. **Objective Length Definition**: Always specify objective length as second parameter: `OBJECTIVE_NAME(STAGE, LENGTH, "Description")`
3. **Single Entry Point**: Use ONLY `QuestManager.updateQuest()` for all quest progression
4. **Smart Dialogue**: Implement progress-based dialogue in `handleDialogue()` method
5. **Quest Completion**: Use `isQuestCompleted()` to check if entire quest is done
6. **Testing**: Test quest progression, completion, and dialogue thoroughly before deployment

## Troubleshooting

### Common Issues
1. **Quest not updating**: Use `QuestManager.updateQuest()` instead of deprecated methods
2. **Double rewards**: Ensure you're only using `QuestManager.updateQuest()` and not calling rewards manually
3. **Quest restarting after completion**: Check that quest completion is properly tracked with `isQuestCompleted()`
4. **Progress not saving**: Ensure quest is added to ALL_QUESTS array
5. **Dialogue not showing**: Verify NPC ID and dialogue content
6. **Storage bloat**: System automatically cleans up old progress data

### Debug Commands
Use the quest manager methods for debugging:
```java
// Check if entire quest is completed
boolean completed = player.getQuestManager().isQuestCompleted("Adventurer's Path");

// Get current objective
String current = player.getQuestManager().getCurrentObjective("Adventurer's Path");

// Check objective progress
int progress = player.getQuestManager().getProgress(MyQuest.OBJECTIVE_NAME);

// Reset quest progress (admin only)
player.getQuestManager().reset(MyQuest.OBJECTIVE_NAME);

// Force complete quest (admin only)
player.getQuestManager().setFinished(MyQuest.OBJECTIVE_NAME);
```

## Future Enhancements

The quest system is designed to be extensible. Future enhancements could include:
- Quest prerequisites and dependencies
- Branching quest paths
- Time-based quest objectives
- Group/party quests
- Quest journals and logs
- Advanced reward systems
