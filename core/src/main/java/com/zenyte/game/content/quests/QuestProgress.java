package com.zenyte.game.content.quests;

import com.google.gson.annotations.Expose;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 * 
 * Represents quest progress data for character file storage.
 * This provides a more readable format than the raw map structure.
 */
public class QuestProgress {
    
    @Expose
    private int stage;

    @Expose
    private int maxStage;

    @Expose
    private QuestStage questStage;

    // Transient fields - not saved to JSON but available for runtime use
    private transient String questName;
    private transient String objectiveName;
    
    /**
     * Default constructor for JSON deserialization
     */
    public QuestProgress() {
    }
    
    /**
     * Constructor for creating quest progress entries
     *
     * @param stage the current stage/progress
     * @param maxStage the maximum stage for completion
     * @param questStage the quest stage (NOT_STARTED, IN_PROGRESS, COMPLETED)
     */
    public QuestProgress(int stage, int maxStage, QuestStage questStage) {
        this.stage = stage;
        this.maxStage = maxStage;
        this.questStage = questStage;
    }

    /**
     * Constructor with quest name and objective name for runtime use
     *
     * @param questName the name of the quest (e.g., "Adventurer's Path") - not saved to JSON
     * @param objectiveName the name of the objective (e.g., "COMPLETE_SLAYER_TASK_TURAEL") - not saved to JSON
     * @param stage the current stage/progress
     * @param maxStage the maximum stage for completion
     * @param questStage the quest stage (NOT_STARTED, IN_PROGRESS, COMPLETED)
     */
    public QuestProgress(String questName, String objectiveName, int stage, int maxStage, QuestStage questStage) {
        this.questName = questName;
        this.objectiveName = objectiveName;
        this.stage = stage;
        this.maxStage = maxStage;
        this.questStage = questStage;
    }
    
    // Getters
    public String getQuestName() {
        return questName;
    }
    
    public String getObjectiveName() {
        return objectiveName;
    }
    
    public int getStage() {
        return stage;
    }
    
    public int getMaxStage() {
        return maxStage;
    }
    
    public QuestStage getQuestStage() {
        return questStage;
    }

    public boolean isCompleted() {
        return stage >= maxStage;
    }
    
    // Setters
    public void setQuestName(String questName) {
        this.questName = questName;
    }
    
    public void setObjectiveName(String objectiveName) {
        this.objectiveName = objectiveName;
    }
    
    public void setStage(int stage) {
        this.stage = stage;
    }

    public void setMaxStage(int maxStage) {
        this.maxStage = maxStage;
    }

    public void setQuestStage(QuestStage questStage) {
        this.questStage = questStage;
    }
    
    /**
     * Creates a unique key for this quest progress entry
     * @return unique key combining quest and objective names
     */
    public String getKey() {
        return questName + ":" + objectiveName;
    }
    
    /**
     * Creates a QuestProgress from a Quest object
     * @param quest the quest objective
     * @param stage the current stage
     * @return QuestProgress object
     */
    public static QuestProgress fromQuest(Quest quest, int stage) {
        // Determine the actual quest stage based on progress
        QuestStage actualStage;
        if (stage == 0) {
            actualStage = QuestStage.NOT_STARTED;
        } else if (stage >= quest.objectiveLength()) {
            actualStage = QuestStage.COMPLETED;
        } else {
            actualStage = QuestStage.IN_PROGRESS;
        }

        return new QuestProgress(
            quest.title(),
            quest.name(),
            stage,
            quest.objectiveLength(),
            actualStage
        );
    }

    /**
     * Creates a minimal QuestProgress for saving (without quest/objective names)
     * @param quest the quest objective
     * @param stage the current stage
     * @return QuestProgress object
     */
    public static QuestProgress forSaving(Quest quest, int stage) {
        // Determine the actual quest stage based on progress
        QuestStage actualStage;
        if (stage == 0) {
            actualStage = QuestStage.NOT_STARTED;
        } else if (stage >= quest.objectiveLength()) {
            actualStage = QuestStage.COMPLETED;
        } else {
            actualStage = QuestStage.IN_PROGRESS;
        }

        return new QuestProgress(stage, quest.objectiveLength(), actualStage);
    }
    
    @Override
    public String toString() {
        return String.format("QuestProgress{questName='%s', objectiveName='%s', stage=%d/%d, questStage=%s}",
                           questName, objectiveName, stage, maxStage, questStage);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        QuestProgress that = (QuestProgress) obj;
        return questName != null ? questName.equals(that.questName) : that.questName == null &&
               objectiveName != null ? objectiveName.equals(that.objectiveName) : that.objectiveName == null;
    }
    
    @Override
    public int hashCode() {
        int result = questName != null ? questName.hashCode() : 0;
        result = 31 * result + (objectiveName != null ? objectiveName.hashCode() : 0);
        return result;
    }
}
