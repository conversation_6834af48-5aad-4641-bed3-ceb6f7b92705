package com.zenyte.game.content.quests;

import com.zenyte.game.world.entity.player.Player;

import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 */
public interface Quest {

    /**
     * Gets the objective description for this quest objective.
     *
     * @return the objective description lines
     */
    String[] objectiveDescription();

    /**
     * Gets the objective length (total progress needed to complete this objective).
     *
     * @return the objective length
     */
    int objectiveLength();

    /**
     * Whether this quest objective uses flagging (bitwise operations) for progress tracking.
     *
     * @return true if flagging is used
     */
    boolean flagging();

    /**
     * Gets the quest stage/difficulty of this objective.
     *
     * @return the quest stage
     */
    QuestStage stage();





    /**
     * Gets the quest title.
     *
     * @return the quest title
     */
    String title();

    /**
     * Gets the name of this quest objective.
     *
     * @return the quest objective name
     */
    String name();



    /**
     * Gets the NPC ID responsible for this specific quest objective's dialogue.
     * Different quest objectives can have different NPCs.
     *
     * @return the NPC ID for this quest objective, or -1 if no specific NPC
     */
    default int getDialogueNpc() {
        return -1;
    }

    /**
     * Gets all dialogue NPC IDs involved in this specific quest objective.
     * This allows quest objectives to have multiple NPCs for dialogue interactions.
     * Only include NPCs that the player needs to talk to - combat NPCs are not needed here.
     *
     * @return array of dialogue NPC IDs for this quest objective, or empty array if none
     */
    default int[] getObjectiveNpcs() {
        int dialogueNpc = getDialogueNpc();
        if (dialogueNpc != -1) {
            return new int[] { dialogueNpc }; // Return single NPC as array for backward compatibility
        }
        return new int[0]; // No NPCs
    }

    /**
     * Handles the dialogue for this quest objective when talking to the appropriate NPC.
     * Override this method to provide custom dialogue handling.
     *
     * @param player the player
     * @param npcId the NPC ID being talked to
     */
    default void handleDialogue(Player player, int npcId) {
        // Default implementation - no special dialogue handling
    }

    /**
     * Gets the map of quest stages to quest objectives.
     *
     * @return the quest stage map
     */
    Map<QuestStage, List<Quest>> map();



    /**
     * Gets the predicate for validating quest objective completion conditions.
     *
     * @return the validation predicate
     */
    Predicate<Player> predicate();

    /**
     * Gets the objective name for progress tracking.
     *
     * @return the objective name
     */
    default String objectiveName() {
        return name();
    }





    /**
     * Gets the object ID for object interactions specific to this quest objective.
     *
     * @return the object ID, or -1 if no specific object
     */
    default int interactionObject() {
        return -1;
    }

    /**
     * Gets the item ID required for this quest objective.
     *
     * @return the item ID, or -1 if no specific item required
     */
    default int requiredItem() {
        return -1;
    }

    /**
     * Gets the amount of the required item needed.
     *
     * @return the required amount, or 0 if no item required
     */
    default int requiredAmount() {
        return 0;
    }

    /**
     * Handles reward distribution when this quest objective is completed.
     * Override this method in quest implementations to give specific rewards.
     *
     * @param player the player who completed the quest objective
     */
    default void giveRewards(Player player) {
        // Default implementation - no rewards
    }

    /**
     * Gets all NPCs involved in this quest.
     * This automatically collects NPCs from all quest objectives by checking each objective's getObjectiveNpcs() method.
     *
     * @return array of NPC IDs used by this quest
     */
    default int[] getQuestNpcs() {
        // Get all quest objectives from the map
        Map<QuestStage, List<Quest>> questMap = map();
        Set<Integer> npcIds = new HashSet<>();

        // Iterate through all stages and collect NPCs from each quest objective
        for (List<Quest> questObjectives : questMap.values()) {
            for (Quest objective : questObjectives) {
                int[] objectiveNpcs = objective.getObjectiveNpcs();
                for (int npcId : objectiveNpcs) {
                    if (npcId != -1) { // Only add valid NPC IDs
                        npcIds.add(npcId);
                    }
                }
            }
        }

        // Convert Set to array
        return npcIds.stream().mapToInt(Integer::intValue).toArray();
    }

    /**
     * Constant for no predicate validation.
     */
    Predicate<Player> NO_PREDICATE = player -> true;
}
