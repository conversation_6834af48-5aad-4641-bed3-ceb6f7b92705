package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;

public class AnimalTamer extends EssencePerk {
    @Override
    public String name() {
        return "Animal Tamer";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_AnimalTamer;
    }

    @Override
    public String description() {
        return "Provides a 5% drop rate boost when a pet is following you.";
    }

    @Override
    public int item() {
        return 10150;
    }
}
