package com.zenyte.game.content.itemupgrade;

import com.zenyte.game.GameInterface;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.ObjectId;
import com.zenyte.game.world.object.WorldObject;

public class UpgradeRackObject implements ObjectAction {

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        GameInterface.UPGRADE_INTERFACE.open(player);
    }

    @Override
        public Object[] getObjects() {
            return new Object[]{ObjectId.WEAPON_RACK_33020};
        }
    }
