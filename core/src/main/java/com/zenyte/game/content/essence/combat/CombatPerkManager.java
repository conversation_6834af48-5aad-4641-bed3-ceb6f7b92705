package com.zenyte.game.content.essence.combat;

import com.near_reality.game.content.shop.ShopCurrencyHandler;
import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.combat.impl.UnknownEssencePerk;
import com.zenyte.game.content.quests.Quest;
import com.zenyte.game.content.quests.quests.AdventurersPath;
import com.zenyte.game.model.shop.ShopCurrency;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.area.DuelArenaArea;

import java.util.ArrayList;
import java.util.Set;

/**
 * This manager class interfaces with Player Perks that are unlocked with sacrifice/exchange points
 * to provide various bonuses in all elements of the game
 */
public class CombatPerkManager {
    public ArrayList<String> unlockedPerks = new ArrayList<>();
    public ArrayList<String> toggleOffPerks = new ArrayList<>();
    private transient final Player player;

    public CombatPerkManager(Player player) {
        this.player = player;
    }

    public void forceUnlock(String perk) {
        if(!unlockedPerks.contains(perk)) {
            player.notification("Combat Perk Manager", "Perk Unlocked:<br><br>" + Colour.WHITE.wrap(perk), 16750623);
            unlockedPerks.add(perk);
        }
    }

    public boolean hasPerk(Class<? extends EssencePerk> lookupPerk) {
        EssencePerk essencePerk = CombatPerkLoader.findPerk(lookupPerk);

        if(player.getArea() != null && player.getArea() instanceof DuelArenaArea)
            return false;

        if(player.inCombatWithPlayer())
            return false;

        return essencePerk.isActive(player) && (essencePerk.isAlwaysUnlocked(player) || unlockedPerks.contains(essencePerk.getClass().getSimpleName()) || unlockedPerks.contains(essencePerk.getAlternateName())) && !toggleOffPerks.contains(essencePerk.getClass().getSimpleName());
    }

    public boolean purchasePerk(Class<?> lookupPerk, int index) {
        EssencePerk essencePerk = CombatPerkLoader.findPerk(lookupPerk);
        if (essencePerk instanceof UnknownEssencePerk) {
            player.sendDeveloperMessage("Unknown error attempting to purchase this perk.");
            return false;
        }
        String perkName = essencePerk.getClass().getSimpleName();

        Set<String> speedPerks = Set.of("MeleeISpeed", "RangedISpeed", "MagicISpeed");
        Set<AdventurersPath> allowedObjectives = Set.of(
                AdventurersPath.UNLOCK_MAGIC_COMBAT_SPEED_PERK,
                AdventurersPath.UNLOCK_RANGED_COMBAT_SPEED_PERK,
                AdventurersPath.UNLOCK_MELEE_COMBAT_SPEED_PERK
        );
        if (speedPerks.contains(perkName)) {
            Quest currentQuest = AdventurersPath.getCurrentObjective(player);
            if (!(currentQuest instanceof AdventurersPath current)
                    || !allowedObjectives.contains(current)) {
                player.sendMessage("<col=ff0000>You can only unlock the combat speed perks during the Adventurer's Path quest!</col>");
                return false;
            }
            switch (current) {
                case UNLOCK_MAGIC_COMBAT_SPEED_PERK -> {
                    if (!perkName.equals("MagicISpeed")) {
                        player.sendMessage("<col=ff0000>You must unlock the Magic I speed perk first!</col>");
                        return false;
                    }
                }
                case UNLOCK_RANGED_COMBAT_SPEED_PERK -> {
                    if (!perkName.equals("RangedISpeed")) {
                        player.sendMessage("<col=ff0000>You must unlock the Ranged I speed perk second!</col>");
                        return false;
                    }
                }
                case UNLOCK_MELEE_COMBAT_SPEED_PERK -> {
                    if (!perkName.equals("MeleeISpeed")) {
                        player.sendMessage("<col=ff0000>You must unlock the Melee I speed perk last!</col>");
                        return false;
                    }
                }
            }
        }
        if (unlockedPerks.contains(perkName)) {
            player.sendMessage("You already have this perk unlocked.");
            return false;
        }
        if (!essencePerk.purchasable(player)) {
            player.sendMessage("You do not meet the requirements to purchase this perk.");
            return false;
        }

        int available = ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player);
        if (essencePerk.price() <= available) {
            ShopCurrencyHandler.remove(ShopCurrency.COMBAT_ESSENCE, player, essencePerk.price());
            unlockedPerks.add(perkName);
            player.notification("Combat Perks Manager", "Combat Perk Unlocked:<br><br>" + Colour.WHITE.wrap(essencePerk.name()), 16750623);
            player.sendMessage("<col=ff0000>You have unlocked " + Colour.BLUE.wrap(essencePerk.name()) + " for " + essencePerk.price() + " Combat Essence.</col>");
            if (player.isDebugging)
                player.sendMessage("Purchased perk: " + perkName+ "with index (itemId): "+ index);
            player.getVarManager().sendVarInstant(4508, ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player));
            player.getVarManager().sendBitInstant(19699 + index, 1);
            player.getVarManager().sendBitInstant(19698, 1);
            player.getPacketDispatcher().sendClientScript(12606, 998, essencePerk.name());
            AdventurersPath.onCombatSpeedPerkUnlocked(player, index);
            return true;
        } else {
            player.sendMessage("You do not have enough Combat Essence.");
            return false;
        }
    }

    public void togglePerk(Class<?> lookupPerk) {
        EssencePerk essencePerk = CombatPerkLoader.findPerk(lookupPerk);
        String perkName = essencePerk.getClass().getSimpleName();
        if(essencePerk instanceof UnknownEssencePerk) {
            player.sendDeveloperMessage("Unknown error attempting to toggle this perk.");
            return;
        }
        if(!unlockedPerks.contains(perkName)) {
            player.sendMessage("You need to purchase this perk first");
            return;
        }
        if(toggleOffPerks.contains(perkName)) {
            toggleOffPerks.remove(perkName);
            player.sendMessage(Colour.RS_GREEN.wrap(essencePerk.name() + " has been toggled ON!"));
        } else {
            toggleOffPerks.add(perkName);
            player.sendMessage(Colour.RS_RED.wrap(essencePerk.name() + " has been toggled OFF!"));
        }
    }


    public void initialize(CombatPerkManager manager) {
        if (manager != null && manager.unlockedPerks != null) {
            unlockedPerks = manager.unlockedPerks;
        }

        if (manager != null && manager.toggleOffPerks != null) {
            toggleOffPerks = manager.toggleOffPerks;
        }
    }
}
