package com.zenyte.game.content.quests;

import com.google.gson.annotations.Expose;

import com.zenyte.game.content.quests.quests.*;
import com.zenyte.game.util.BitUtils;

import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

import com.zenyte.plugins.Listener;
import com.zenyte.plugins.ListenerType;
import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 */
@SuppressWarnings("unused")
public final class QuestManager {
    private transient Player player;
    @Expose
    private final Map<String, String> currentQuestObjectives = new HashMap<>();

    @Expose
    private final Map<String, Integer> questObjectiveProgress = new HashMap<>();

    @Expose
    private final Set<String> completedQuests = new HashSet<>();



    public QuestManager(final Player player) {
        this.player = player;
    }

    public void initialize(final Player player, final Player parser) {
        this.player = player;
        final QuestManager parserData = parser.getQuestManager();
        if (parserData == null) {
            return;
        }
        currentQuestObjectives.putAll(parserData.currentQuestObjectives);
        questObjectiveProgress.putAll(parserData.questObjectiveProgress);
        completedQuests.addAll(parserData.completedQuests);
        cleanupProgressData();
    }

    /**
     * Gets the current progress of a quest objective in either a total value or a mask.
     *
     * @param quest the quest objective.
     * @return current progress within the objective.
     */
    public int getProgress(final Quest quest) {
        String questName = quest.title();
        if (completedQuests.contains(questName)) {
            return quest.objectiveLength();
        }
        String currentObjective = currentQuestObjectives.get(questName);
        if (currentObjective == null) {
            return 0;
        }
        if (currentObjective.equals(quest.name())) {
            return questObjectiveProgress.getOrDefault(getProgressKey(quest), 0);
        }
        if (isObjectiveBeforeCurrent(quest, currentObjective)) {
            return quest.objectiveLength(); // Completed
        }
        return 0;
    }

    /**
     * Generates a unique key for storing objective progress
     */
    private String getProgressKey(Quest quest) {
        return quest.title() + ":" + quest.name();
    }

    /**
     * Cleans up old progress data - only keeps progress for current objectives
     * This optimizes storage by removing completed objective progress
     */
    private void cleanupProgressData() {
        Set<String> validKeys = new HashSet<>();
        for (Map.Entry<String, String> entry : currentQuestObjectives.entrySet()) {
            String questTitle = entry.getKey();
            String objectiveName = entry.getValue();
            validKeys.add(questTitle + ":" + objectiveName);
        }
        questObjectiveProgress.entrySet().removeIf(entry -> !validKeys.contains(entry.getKey()));
    }

    /**
     * Checks if a quest objective comes before the current objective in the quest sequence
     */
    private boolean isObjectiveBeforeCurrent(Quest quest, String currentObjectiveName) {
        Quest[] allObjectives = getQuestObjectives(quest.title());
        boolean foundQuest = false;
        for (Quest objective : allObjectives) {
            if (objective.name().equals(quest.name())) {
                foundQuest = true;
            } else if (objective.name().equals(currentObjectiveName)) {
                return foundQuest;
            }
        }
        return false;
    }

    /**
     * Gets all quest objectives for a given quest title
     */
    private Quest[] getQuestObjectives(String questTitle) {
        for (final Quest[] questSet : ALL_QUESTS) {
            if (questSet.length > 0 && questSet[0].title().equals(questTitle)) {
                return questSet;
            }
        }
        return new Quest[0];
    }

    /**
     * Refreshes the quest progress (simplified - no GUI components).
     *
     * @param quest the quest to refresh.
     */
    public void refresh(final Quest quest) {
        // TODO: Setup this as needed
        // Quest progress is tracked through the map and dialogue system
    }

    /**
     * Gets a quest objective that has dialogue for the specified NPC.
     *
     * @param npcId the NPC ID
     * @param player the player (to check quest progress)
     * @return the appropriate quest objective for dialogue, or null if none found
     */
    public static Quest getQuestObjectiveForNpc(final int npcId, final Player player) {
        final QuestManager manager = player.getQuestManager();

        for (final Quest[] questSet : ALL_QUESTS) {
            for (final Quest quest : questSet) {
                if (quest.getDialogueNpc() == npcId) {
                    if (!manager.isCompleted(quest)) {
                        return quest;
                    }
                }
            }
        }
        return null;
    }



    public static final Quest[][] ALL_QUESTS = new Quest[][] {
        AdventurersPath.VALUES
    };

    @Listener(type = ListenerType.LOBBY_CLOSE)
    private static void onLogin(final Player player) {
        final QuestManager manager = player.getQuestManager();
        for (final Quest[] quest : ALL_QUESTS) {
            manager.refresh(quest[0]);
        }
    }

    public void finish(final Quest quest) {
        update(quest, quest.objectiveLength(), true);
        refresh(quest);
    }

    public void setFinished(final Quest quest) { // Mark the entire quest as completed
        completedQuests.add(quest.title());
        currentQuestObjectives.remove(quest.title());
        Quest[] allObjectives = getQuestObjectives(quest.title());
        for (Quest objective : allObjectives) { //Clears all data for this quest since it's completed
            questObjectiveProgress.remove(getProgressKey(objective));
        }
    }

    public void reset(final Quest quest) { // Reset quest by removing it from current objectives and completed quests
        currentQuestObjectives.remove(quest.title());
        completedQuests.remove(quest.title());
        Quest[] allObjectives = getQuestObjectives(quest.title());
        for (Quest objective : allObjectives) {
            questObjectiveProgress.remove(getProgressKey(objective));
        }

        refresh(quest);
    }

    /**
     * MAIN QUEST UPDATE METHOD - Use this for ALL quest progression
     * This is the single entry point for updating quest progress and giving rewards.
     * Rewards are automatically handled when objectives are completed.
     *
     * @param player the player
     * @param questClass the quest class (e.g., AdventurersPath.class)
     * @param objectiveName the objective name (e.g., "COMPLETE_SLAYER_TASK_TURAEL")
     * @param objectiveLength the objective length amount (optional, defaults to 1)
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static void updateQuest(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName, int objectiveLength) {
        //NOTE: objectiveLength is how many objectiveLengths to add (+ 1 = current progress + 1, + 2 = current progress + 2, etc.)
        try {
            Enum questEnum = Enum.valueOf((Class) questClass, objectiveName);
            Quest quest = (Quest) questEnum;
            QuestManager manager = player.getQuestManager();
            if (manager.isCompleted(quest)) {
                return;
            }
            manager.update(quest, objectiveLength, false);
        } catch (IllegalArgumentException e) {
            player.sendMessage("Quest objective not found: " + objectiveName);
        } catch (Exception e) {
            player.sendMessage("Error updating quest: " + e.getMessage());
        }
    }

    /**
     * MAIN QUEST UPDATE METHOD - Simplified version with default objective length of 1.
     * Use this for most quest progression where you just need to advance by 1 step.
     */
    public static void updateQuest(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName) {
        updateQuest(player, questClass, objectiveName, 1);
    }

    /**
     * CONDITIONAL QUEST UPDATE - Updates quest only if player is on specific objective with specific progress
     * This is useful for checking prerequisites before updating quest progress.
     *
     * @param player the player
     * @param questClass the quest class (e.g., AdventurersPath.class)
     * @param objectiveName the objective name to check and update
     * @param requiredProgress the required current progress to allow the update
     * @param updateAmount the amount to update by (defaults to 1)
     * @return true if quest was updated, false if conditions weren't met
     */
    public static boolean updateQuestIfOnObjectiveWithProgress(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName, int requiredProgress, int updateAmount) {
        try {
            Enum questEnum = Enum.valueOf((Class) questClass, objectiveName);
            Quest quest = (Quest) questEnum;
            QuestManager manager = player.getQuestManager();
            if (!manager.isOnObjectiveWithProgress(quest, requiredProgress)) {
                return false;
            }
            manager.update(quest, updateAmount, false);
            return true;
        } catch (IllegalArgumentException e) {
            player.sendMessage("Quest objective not found: " + objectiveName);
            return false;
        } catch (Exception e) {
            player.sendMessage("Error updating quest: " + e.getMessage());
            return false;
        }
    }

    /**
     * CONDITIONAL QUEST UPDATE - Simplified version with default update amount of 1
     */
    public static boolean updateQuestIfOnObjectiveWithProgress(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName, int requiredProgress) {
        return updateQuestIfOnObjectiveWithProgress(player, questClass, objectiveName, requiredProgress, 1);
    }

    /**
     * INTERNAL METHOD - Updates the requested quest objective if it hasn't already been completed.
     * This method handles rewards automatically. Use the static updateQuest() method instead.
     *
     * @param quest the quest objective to update.
     * @param amount the amount to add to current progress, or the flag to append if the quest is flag-based.
     * @param force whether to force the update regardless of completion status.
     */
    public void update(final Quest quest, final int amount, final boolean force) {
        final int progress = getProgress(quest);
        final Predicate<Player> predicate = quest.predicate();

        if (!force && (progress >= quest.objectiveLength() || predicate != null && !predicate.test(player))) {
            return;
        }
        int newValue;
        if (quest.flagging()) {
            newValue = progress | amount;
            if (!force && newValue == progress) {
                return;
            }
        } else {
            newValue = progress + amount;
        }
        newValue = Math.min(newValue, quest.objectiveLength());
        questObjectiveProgress.put(getProgressKey(quest), newValue);
        if (player.isDebugging)
            player.sendMessage("Progress: " + progress);
        if (newValue >= quest.objectiveLength()) { // If ObjectiveLength is now the same as the newValue, the objective is completed
            final String objectiveDescription = String.join(" ", quest.objectiveDescription());
            player.sendMessage("<col=dc143c>Well done! You have completed the objective: " + objectiveDescription + " Your Quest Log has been updated.");
            quest.giveRewards(player);
            moveToNextObjective(quest);
        }

        refresh(quest);
    }

    /**
     * Moves to the next objective in the quest sequence when current objective is completed
     */
    private void moveToNextObjective(Quest completedQuest) {
        Quest[] allObjectives = getQuestObjectives(completedQuest.title());
        for (int i = 0; i < allObjectives.length - 1; i++) {
            if (allObjectives[i].name().equals(completedQuest.name())) {
                Quest nextObjective = allObjectives[i + 1];
                currentQuestObjectives.put(completedQuest.title(), nextObjective.name());
                questObjectiveProgress.remove(getProgressKey(completedQuest));
                questObjectiveProgress.put(getProgressKey(nextObjective), 0);
                return;
            }
        }
        completedQuests.add(completedQuest.title()); // If we reach the end, mark the quest as completed
        currentQuestObjectives.remove(completedQuest.title());
        for (Quest objective : allObjectives) {
            questObjectiveProgress.remove(getProgressKey(objective));
        }
    }

    public boolean isCompleted(Quest quest) {
        return getProgress(quest) == quest.objectiveLength();
    }

    /**
     * Checks if an entire quest is completed
     * @param questTitle the quest title (e.g., "Adventurer's Path")
     * @return true if the entire quest is completed
     */
    public boolean isQuestCompleted(String questTitle) {
        return completedQuests.contains(questTitle);
    }

    /**
     * Gets all current quest objectives for debugging/display
     * @return map of quest names to current objective names
     */
    public Map<String, String> getCurrentQuestObjectives() {
        return new HashMap<>(currentQuestObjectives);
    }

    /**
     * Gets all quest objective progress for debugging/display
     * @return map of progress keys to progress values
     */
    public Map<String, Integer> getQuestObjectiveProgress() {
        return new HashMap<>(questObjectiveProgress);
    }

    /**
     * Gets all completed quests for debugging/display
     * @return set of completed quest titles
     */
    public Set<String> getCompletedQuests() {
        return new HashSet<>(completedQuests);
    }

    /**
     * Checks if player is currently on a specific objective with specific progress
     * @param quest the quest objective to check
     * @param requiredProgress the required progress (objective length) to match
     * @return true if player is on this objective with the specified progress
     */
    public boolean isOnObjectiveWithProgress(Quest quest, int requiredProgress) {
        String questTitle = quest.title();
        String currentObjective = currentQuestObjectives.get(questTitle);
        if (currentObjective == null || !currentObjective.equals(quest.name())) {
            return false;
        }
        int currentProgress = questObjectiveProgress.getOrDefault(getProgressKey(quest), 0);
        return currentProgress == requiredProgress;
    }

    /**
     * Checks if player is currently on a specific objective (any progress)
     * @param quest the quest objective to check
     * @return true if player is currently on this objective
     */
    public boolean isOnObjective(Quest quest) {
        String questTitle = quest.title();
        String currentObjective = currentQuestObjectives.get(questTitle);
        return currentObjective != null && currentObjective.equals(quest.name());
    }

    /**
     * Gets the current objective for a specific quest
     * @param questTitle the quest title
     * @return current objective name or null if quest not started/completed
     */
    public String getCurrentObjective(String questTitle) {
        return currentQuestObjectives.get(questTitle);
    }

    /**
     * Starts a quest by setting its first objective as current
     * @param quest the first quest objective
     */
    public void startQuest(Quest quest) {
        currentQuestObjectives.put(quest.title(), quest.name());
        // Initialize progress for the starting objective
        questObjectiveProgress.put(getProgressKey(quest), 0);
    }

    public boolean isAllCompleted() {
        for (final Quest[] set : ALL_QUESTS) {
            for (final Quest quest : set) {
                if (!isCompleted(quest)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean isAllCompleted(QuestStage stage) {
        for (final Quest[] set : ALL_QUESTS) {
            for (final Quest quest : set) {
                if (quest.stage() == stage && !isCompleted(quest)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Gets all NPCs involved in all quests.
     * This method collects NPCs from all quest classes.
     *
     * @return array of all NPC IDs used by all quests
     */
    public static int[] getNpcs() {
        Set<Integer> allNpcs = new HashSet<>();
        for (final Quest[] questSet : ALL_QUESTS) {
            if (questSet.length > 0) {
                Quest firstQuest = questSet[0];
                int[] questNpcs = firstQuest.getQuestNpcs();
                for (int npcId : questNpcs) {
                    allNpcs.add(npcId);
                }
            }
        }
        return allNpcs.stream().mapToInt(Integer::intValue).toArray();
    }

}
