package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class HoleyMoley extends <PERSON><PERSON>cePerk {
    @Override
    public String name() {
        return "<PERSON><PERSON>";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_HoleyMoley;
    }

    @Override
    public String description() {
        return "Doubles Mole Body parts from Giant Mole, Reduces Private Instance Fee of Giant Mole, and prevents Giant Mole from digging in Private Instances.";
    }

    @Override
    public int item() {
        return ItemId.MOLE_CLAW;
    }
}
