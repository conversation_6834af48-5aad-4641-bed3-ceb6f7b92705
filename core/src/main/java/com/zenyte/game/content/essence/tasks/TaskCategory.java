package com.zenyte.game.content.essence.tasks;

import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.Skills;
import com.zenyte.utils.TextUtils;

import static com.zenyte.game.content.essence.tasks.TaskDifficulty.*;

/**
 * <AUTHOR> | 02/05/2019 | 22:40
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>
 * <AUTHOR> (Discord: astra4)
 */
public enum TaskCategory {
    SKILLING(player -> new TaskDetails(null, (Object) null)),
    COMBAT(player -> {
        final int combatLevel = player.getCombatLevel();
        final TaskDifficulty difficulty = combatLevel >= 100 ? ELITE : combatLevel >= 75 ? HARD : combatLevel >= 40 ? MEDIUM : EASY;
        return new TaskDetails(difficulty, combatLevel);
    })
    ;

    private final DetailsDetermination determination;
    public static final TaskCategory[] all = values();

    public TaskDetails getDetails(final Player player) {
        return determination.getDetails(player);
    }


    private interface DetailsDetermination {
        TaskDetails getDetails(final Player player);
    }

    @Override
    public String toString() {
        return TextUtils.formatName(name().toLowerCase());
    }

    TaskCategory(DetailsDetermination determination) {
        this.determination = determination;
    }
}
