package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

public class Pyromaniac extends EssencePerk {
    public static final String NO_PERK_MESSAGE = "You must have Pyromaniac unlocked to be able to do that.";

    public static boolean rollPage() {
        return Utils.random(24) == 0;
    }

    public static boolean rollArson() {
        return Utils.random(14) == 0;
    }

    @Override
    public String name() {
        return "Pyromaniac";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_Pyromaniac;
    }

    @Override
    public String description() {
        return "Gives you a 1 in 10 chance of burning all your logs at once on bonfire and use any fire as a bonfire. It also can award burnt pages randomly.";
    }

    @Override
    public int item() {
        return 590;
    }
}
