package com.zenyte.game.content.quests;

import com.zenyte.game.content.achievementdiary.Diary;
import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.container.impl.Inventory;
import com.zenyte.game.content.quests.quests.AdventurersPath;
import com.zenyte.plugins.dialogue.OptionsMenuD;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 *
 * Utility class for quest-related operations including:
 * - Quest progress tracking and display
 * - NPC management for quests
 * - Quest completion checking
 * - Simplified quest update methods
 *
 * USAGE EXAMPLES:
 *
 * // Update a quest:
 * QuestUtil.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
 *
 * // Check if NPC is a quest NPC:
 * if (QuestUtil.isQuestNpc(npcId)) { ... }
 *
 * // Get quest progress display:
 * String display = QuestUtil.getQuestObjectiveDisplay(player, questObjective);
 *
 * // Get all quest NPCs:
 * int[] allNpcs = QuestUtil.getAllQuestNpcs();
 */

public final class QuestUtil {
    /**
     * Checks if the player has the required item and amount for a quest objective.
     *
     * @param player the player to check
     * @param quest the quest objective
     * @return true if the player has the required items
     */
    public static boolean hasRequiredItems(final Player player, final Quest quest) {
        if (quest.requiredItem() == -1) {
            return true; // No item required
        }
        
        final Inventory inventory = player.getInventory();
        return inventory.getAmountOf(quest.requiredItem()) >= quest.requiredAmount();
    }

    /**
     * Removes the required items from the player's inventory for a quest objective.
     *
     * @param player the player
     * @param quest the quest objective
     * @return true if items were successfully removed
     */
    public static boolean removeRequiredItems(final Player player, final Quest quest) {
        if (quest.requiredItem() == -1) {
            return true; // No item required
        }
        
        if (!hasRequiredItems(player, quest)) {
            return false;
        }
        
        final Inventory inventory = player.getInventory();
        inventory.deleteItem(new Item(quest.requiredItem(), quest.requiredAmount()));
        return true;
    }

    /**
     * Gets the display name for a quest objective based on its completion status.
     *
     * @param player the player
     * @param quest the quest objective
     * @return the formatted display name
     */
    public static String getQuestObjectiveDisplay(final Player player, final Quest quest) {
        final QuestManager manager = player.getQuestManager();
        final boolean completed = manager.isCompleted(quest);
        final String objectiveText = String.join(" ", quest.objectiveDescription());

        if (completed) {
            return "<str>" + objectiveText + "</str>";
        } else {
            final int objectiveProgress = manager.getProgress(quest);
            if (objectiveProgress > 0) {
                return "<col=ffff00>" + objectiveText + " (" + objectiveProgress + "/" + quest.objectiveLength() + ")</col>";
            } else {
                return objectiveText; // plain (no color) when not started
            }
        }
    }

    /*
     * Gets the Object Description (text) for a quest objective and returns in the @String format
     */

    public static String getQuestObjectiveSteps(final Player player, final Quest quest) {
        final String objectiveText = String.join(" ", quest.objectiveDescription());
                return "Current Objective: "+objectiveText;
    }

    /**
     * Gets the overall quest completion percentage.
     *
     * @param player the player
     * @return the completion percentage (0-100)
     */
    public static int getQuestCompletionPercentage(final Player player) {
        final QuestManager manager = player.getQuestManager();
        int totalQuests = 0;
        int completedQuests = 0;
        
        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            for (final Quest quest : questSet) {
                totalQuests++;
                if (manager.isCompleted(quest)) {
                    completedQuests++;
                }
            }
        }
        
        if (totalQuests == 0) {
            return 100;
        }
        
        return (completedQuests * 100) / totalQuests;
    }

    /**
     * Gets the number of completed quests for a specific stage.
     *
     * @param player the player
     * @param stage the quest stage
     * @return the number of completed quests in the stage
     */
    public static int getCompletedQuestsInStage(final Player player, final QuestStage stage) {
        final QuestManager manager = player.getQuestManager();
        int completed = 0;

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            for (final Quest quest : questSet) {
                if (quest.stage() == stage && manager.isCompleted(quest)) {
                    completed++;
                }
            }
        }

        return completed;
    }

    /**
     * Gets the total number of quests in a specific stage.
     *
     * @param stage the quest stage
     * @return the total number of quests in the stage
     */
    public static int getTotalQuestsInStage(final QuestStage stage) {
        int total = 0;

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            for (final Quest quest : questSet) {
                if (quest.stage() == stage) {
                    total++;
                }
            }
        }

        return total;
    }

    /**
     * Checks if a player meets the requirements to start a quest.
     *
     * @param player the player
     * @param quest the quest to check
     * @return true if the player can start the quest
     */
    public static boolean canStartQuest(final Player player, final Quest quest) {
        final QuestManager manager = player.getQuestManager();
        if (manager.isCompleted(quest)) {
            return false;
        }
        if (manager.getProgress(quest) > 0) {
            return false;
        }
        if (quest.predicate() != null && !quest.predicate().test(player)) {
            return false;
        }
        return true;
    }

    /**
     * Formats a quest stage name for display.
     *
     * @param stage the quest stage
     * @return the formatted stage name
     */
    public static String formatStageName(final QuestStage stage) {
        switch (stage) {
            case NOT_STARTED:
                return "Not Started";
            case IN_PROGRESS:
                return "In Progress";
            case COMPLETED:
                return "Completed";
            default:
                return stage.toString();
        }
    }

    /**
     * Gets all NPCs involved in all quests.
     * This is a convenience method that calls QuestManager.getNpcs().
     *
     * @return array of all NPC IDs used by all quests
     */
    public static int[] getAllQuestNpcs() {
        return QuestManager.getNpcs();
    }

    /**
     * Gets all NPCs involved in a specific quest.
     *
     * @param questClass the quest enum class (e.g., AdventurersPath.class)
     * @return array of NPC IDs used by the quest
     */
    public static int[] getQuestNpcs(final Class<? extends Enum<? extends Quest>> questClass) {
        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            if (questSet.length > 0 && questSet[0].getClass() == questClass) {
                return questSet[0].getQuestNpcs();
            }
        }
        return new int[0];
    }

    /**
     * Checks if an NPC is involved in any quest.
     *
     * @param npcId the NPC ID to check
     * @return true if the NPC is used by any quest
     */
    public static boolean isQuestNpc(final int npcId) {
        final int[] allNpcs = getAllQuestNpcs();
        for (final int questNpcId : allNpcs) {
            if (questNpcId == npcId) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets the quest objective that should handle dialogue for a specific NPC.
     *
     * @param player the player
     * @param npcId the NPC ID
     * @return the quest objective that should handle dialogue, or null if none
     */
    public static Quest getQuestObjectiveForNpc(final Player player, final int npcId) {
        return QuestManager.getQuestObjectiveForNpc(npcId, player);
    }

    /**
     * Gets the number of quests we have available
     *
     * @return int of amount of quests
     */
    public static int getQuestsAvailable() {
        return QuestManager.ALL_QUESTS.length;
    }

    /**
     * Gets the number of quests the player has fully completed.
     *
     * @param player the player
     * @return number of completed quests
     */
    public static int getQuestsCompleted(final Player player) {
        int count = 0;
        final QuestManager manager = player.getQuestManager();

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            boolean allObjectivesComplete = true;

            for (final Quest quest : questSet) {
                if (!manager.isCompleted(quest)) {
                    allObjectivesComplete = false;
                    break;
                }
            }

            if (allObjectivesComplete) {
                count++;
            }
        }
        return count;
    }

    /**
     * Gets a formatted summary of all quest progress for a player.
     *
     * @param player the player
     * @return formatted quest progress summary
     */
    public static String getQuestProgressSummary(final Player player) {
        final StringBuilder summary = new StringBuilder();
        summary.append("Quest Progress Summary:\n");

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            if (questSet.length > 0) {
                final Quest firstQuest = questSet[0];
                summary.append("\n").append(firstQuest.title()).append(":\n");

                for (final Quest quest : questSet) {
                    final String display = getQuestObjectiveDisplay(player, quest);
                    summary.append("  - ").append(display).append("\n");
                }
            }
        }

        return summary.toString();
    }

    /**
     * Opens the quest progress summary in the player's journal.
     *
     * @param player the player
     */
    public static void openQuestProgressSummary(final Player player) {
        List<String> info = new ArrayList<>();
        info.add("Quest Progress Summary:");

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            if (questSet.length > 0) {
                final Quest firstQuest = questSet[0];
                info.add("");
                info.add(firstQuest.title() + ":");

                for (final Quest quest : questSet) {
                    final String display = getQuestObjectiveDisplay(player, quest);
                    info.add("  - " + display);
                }
            }
        }

        Diary.sendJournal(player, "Quest Progress", info);
    }

    /**
     * Opens a new OptionsMenuD to select a quest to view its progress
     *
     * @param player
     */
    public static void openQuestSelector(final Player player) {
        final List<String> questTitles = new ArrayList<>();
        final List<Quest[]> questGroups = new ArrayList<>();

        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            if (questSet.length > 0) {
                questTitles.add(questSet[0].title());
                questGroups.add(questSet);
            }
        }

        player.getDialogueManager().start(new OptionsMenuD(player, "Select a Quest", questTitles.toArray(new String[0])) {
            @Override
            public void handleClick(final int slotId) {
                if (slotId < 0 || slotId >= questGroups.size()) {
                    return;
                }
                Quest[] selectedSet = questGroups.get(slotId);
                openQuestProgressForSet(player, selectedSet);
            }
        });
    }

    /**
     * Opens the quest progress journal for a specific quest set.
     */
    public static void openQuestProgressForSet(final Player player, final Quest[] questSet) {
        List<String> info = new ArrayList<>();

        if (questSet.length > 0) {
            info.add("Objectives:");
            info.add("");
            for (final Quest quest : questSet) {
                final String display = getQuestObjectiveDisplay(player, quest);
                info.add("  - " + display);
            }
        }

        Diary.sendJournal(player, questSet[0].title(), info);
    }
}
