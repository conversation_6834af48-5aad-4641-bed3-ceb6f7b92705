package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;

public class CrystalCatalyst extends <PERSON>ssencePerk {
    @Override
    public String name() {
        return "Crystal Catalyst";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_CrystalCatalyst;
    }

    @Override
    public String description() {
        return "Provides an additional roll when completing the Gauntlet and decreases the rarity of Enhanced Crystal Weapon Seed by 10%.";
    }

    @Override
    public int item() {
        return 25862;
    }
}
