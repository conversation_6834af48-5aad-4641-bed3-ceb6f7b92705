package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.content.skills.mining.MiningDefinitions;
import com.zenyte.game.content.skills.woodcutting.AxeDefinitions;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;

public class SwissArmyMan extends EssencePerk {
    @Override
    public String name() {
        return "SwissArmyMan";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SwissArmyMan;
    }

    @Override
    public String description() {
        return "Provides the ability to chop, mine and catch without tools. Also allows you to attack Guardians in CoX without a Pickaxe.";
    }

    @Override
    public int item() {
        return 1;
    }

    public static MiningDefinitions.PickaxeDefinitions pickaxeForLevel(Player p) {
        int miningLevel = p.getSkills().getLevel(SkillConstants.MINING);
        int smithingLevel = p.getSkills().getLevel(SkillConstants.SMITHING);

        if (miningLevel >= 61) {
            if (smithingLevel >= 85) {
                return MiningDefinitions.PickaxeDefinitions.INFERNAL;
            }
            return MiningDefinitions.PickaxeDefinitions.DRAGON;
        }
        if (miningLevel >= 41) {
            return MiningDefinitions.PickaxeDefinitions.RUNE;
        }
        if (miningLevel >= 31) {
            return MiningDefinitions.PickaxeDefinitions.ADAMANT;
        }
        if (miningLevel >= 21) {
            return MiningDefinitions.PickaxeDefinitions.MITHRIL;
        }
        if (miningLevel >= 11) {
            return MiningDefinitions.PickaxeDefinitions.BLACK;
        }
        return MiningDefinitions.PickaxeDefinitions.IRON;
    }

    public static AxeDefinitions getAxeForLevel(Player p) {
        int level = p.getSkills().getLevel(SkillConstants.WOODCUTTING);
        int firemakingLevel = p.getSkills().getLevel(SkillConstants.FIREMAKING);

        if (level >= 61) {
            if (firemakingLevel >= 85) {
                return AxeDefinitions.INFERNAL;
            }
            return AxeDefinitions.DRAGON;
        }
        if (level >= 41) {
            return AxeDefinitions.RUNE;
        }
        if (level >= 31) {
            return AxeDefinitions.ADAMANT;
        }
        if (level >= 21) {
            return AxeDefinitions.MITHRIL;
        }
        if (level >= 11) {
            return AxeDefinitions.BLACK;
        }
        if (level >= 6) {
            return AxeDefinitions.STEEL;
        }
        return AxeDefinitions.IRON;
    }
}
