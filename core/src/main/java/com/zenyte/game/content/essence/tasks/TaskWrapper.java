package com.zenyte.game.content.essence.tasks;

import com.zenyte.game.content.essence.tasks.type.CombatTask;
import com.zenyte.game.content.essence.tasks.type.Task;
import com.zenyte.game.content.essence.tasks.type.SkillingTask;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> | 03/05/2019 | 22:55
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>
 */
public class TaskWrapper {
    public static Map<String, Task> challenges = new HashMap<>();

    static {
        for (final SkillingTask challenge : SkillingTask.all) {
            challenges.put(challenge.name(), challenge);
        }
//        for (final CombatTask challenge : CombatTask.all) {
//            challenges.put(challenge.name(), challenge);
//        }
    }

    public static Task get(final String name) {
        return challenges.get(name);
    }
}
