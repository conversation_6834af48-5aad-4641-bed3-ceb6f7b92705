package com.zenyte.game.content.itemupgrade;

import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;

enum UpgradableItems {

        //W1(UpgradeCategory.WEAPON, ItemId.DARK_BOW_BH, 100, new Item[]{ new Item(11235,1), new Item(13307, 10000)}),
        //W4(UpgradeCategory.WEAPON, CustomItemId.RED_TWISTED_BOW, 100, new Item[]{ new Item(ItemId.TWISTED_BOW,1), new Item(CustomItemId.RED_TWISTED_BOW_PAINT, 1)}),
        //W5(UpgradeCategory.WEAPON, ItemId.DRAGON_CLAWS_OR, 100, new Item[]{ new Item(ItemId.DRAGON_CLAWS,1)}),
        //W10(UpgradeCategory.WEAPON, ItemId.DINHS_BLAZING_BULWARK, 100, new Item[]{new Item(ItemId.DINHS_BULWARK)}),

        // Weapons
        W3(UpgradeCategory.WEAPON, ItemId.BOW_OF_FAERDHINEN_C, 100, new Item[]{ new Item(ItemId.BOW_OF_FAERDHINEN,1), new Item(ItemId.CRYSTAL_SHARD, 2000)}),
        W7(UpgradeCategory.WEAPON, ItemId.BLADE_OF_SAELDOR_C, 100, new Item[]{ new Item(ItemId.BLADE_OF_SAELDOR,1), new Item(ItemId.CRYSTAL_SHARD, 2000)}),
        W6(UpgradeCategory.WEAPON, ItemId.HOLY_GHRAZI_RAPIER, 100, new Item[]{ new Item(ItemId.GHRAZI_RAPIER,1), new Item(ItemId.HOLY_ORNAMENT_KIT, 1)}),
        W8(UpgradeCategory.WEAPON, ItemId.OSMUMTENS_FANG_OR, 100, new Item[]{ new Item(ItemId.OSMUMTENS_FANG,1), new Item(ItemId.CURSED_PHALANX, 1)}),
        W9(UpgradeCategory.WEAPON, ItemId.HOLY_SCYTHE_OF_VITUR, 100, new Item[]{ new Item(ItemId.SCYTHE_OF_VITUR, 1), new Item(ItemId.HOLY_ORNAMENT_KIT, 1)}),
        W10(UpgradeCategory.WEAPON, ItemId.SANGUINE_SCYTHE_OF_VITUR, 100, new Item[]{new Item(ItemId.SCYTHE_OF_VITUR, 1), new Item(ItemId.SANGUINE_ORNAMENT_KIT, 1)}),
        W11(UpgradeCategory.WEAPON, ItemId.IBANS_STAFF_U, 100, new Item[]{new Item(ItemId.IBANS_STAFF)}, new Item(995, 1000000)),

    // Armour
        A1(UpgradeCategory.ARMOUR, ItemId.HELM_OF_NEITIZNOT_OR, 50, new Item[]{new Item(ItemId.HELM_OF_NEITIZNOT)}),
        A2(UpgradeCategory.ARMOUR, ItemId.SANGUINE_TORVA_FULL_HELM, 100, new Item[]{new Item(ItemId.TORVA_FULLHELM,1), new Item(ItemId.SANGUINE_ORNAMENT_KIT,1)}),
        A3(UpgradeCategory.ARMOUR, ItemId.MASORI_MASK_F, 100, new Item[]{new Item(ItemId.MASORI_MASK,1), new Item(ItemId.MASORI_CRAFTING_KIT,1)}),
        A4(UpgradeCategory.ARMOUR, ItemId.TWISTED_ANCESTRAL_HAT, 100, new Item[]{new Item(ItemId.ANCESTRAL_HAT,1), new Item(ItemId.TWISTED_ANCESTRAL_COLOUR_KIT,1)}),
        A7(UpgradeCategory.ARMOUR, ItemId.BANDOS_CHESTPLATE_OR, 50, new Item[]{new Item(ItemId.BANDOS_CHESTPLATE,1)}),
        A5(UpgradeCategory.ARMOUR, ItemId.FIGHTER_TORSO_OR, 50, new Item[]{new Item(ItemId.FIGHTER_TORSO)}),
        A8(UpgradeCategory.ARMOUR, ItemId.SANGUINE_TORVA_PLATEBODY, 100, new Item[]{new Item(ItemId.TORVA_PLATEBODY,1), new Item(ItemId.SANGUINE_ORNAMENT_KIT,1)}),
        A9(UpgradeCategory.ARMOUR, ItemId.MASORI_BODY_F, 100, new Item[]{new Item(ItemId.MASORI_BODY,1), new Item(ItemId.MASORI_CRAFTING_KIT,1)}),
        A11(UpgradeCategory.ARMOUR, ItemId.TWISTED_ANCESTRAL_ROBE_TOP, 100, new Item[]{new Item(ItemId.ANCESTRAL_ROBE_TOP,1), new Item(ItemId.TWISTED_ANCESTRAL_COLOUR_KIT,1)}),
        A12(UpgradeCategory.ARMOUR, ItemId.BANDOS_TASSETS_OR, 50, new Item[]{new Item(ItemId.BANDOS_TASSETS,1)}),
        A16(UpgradeCategory.ARMOUR, 32144, 50, new Item[]{new Item(ItemId.BANDOS_TASSETS,1)}),
        A13(UpgradeCategory.ARMOUR, ItemId.SANGUINE_TORVA_PLATELEGS, 100, new Item[]{new Item(ItemId.TORVA_PLATELEGS,1), new Item(ItemId.SANGUINE_ORNAMENT_KIT,1)}),
        A14(UpgradeCategory.ARMOUR, ItemId.MASORI_CHAPS_F, 100, new Item[]{new Item(ItemId.MASORI_CHAPS_F,1), new Item(ItemId.MASORI_CRAFTING_KIT,1)}),
        A15(UpgradeCategory.ARMOUR, ItemId.TWISTED_ANCESTRAL_ROBE_BOTTOM, 100, new Item[]{new Item(ItemId.ANCESTRAL_ROBE_BOTTOM,1), new Item(ItemId.TWISTED_ANCESTRAL_COLOUR_KIT,1)}),

        // Jewellery
        J1(UpgradeCategory.JEWELLERY, ItemId.AMULET_OF_TORTURE_OR, 75, new Item[]{new Item(ItemId.AMULET_OF_TORTURE, 2)}),
        J2(UpgradeCategory.JEWELLERY, ItemId.TORMENTED_BRACELET_OR, 75, new Item[]{new Item(ItemId.TORMENTED_BRACELET, 2)}),
        J3(UpgradeCategory.JEWELLERY, ItemId.NECKLACE_OF_ANGUISH_OR, 75, new Item[]{new Item(ItemId.NECKLACE_OF_ANGUISH, 2)}),
        J4(UpgradeCategory.JEWELLERY, ItemId.RING_OF_SUFFERING_I, 75, new Item[]{new Item(ItemId.RING_OF_SUFFERING, 2)}),
        J7(UpgradeCategory.JEWELLERY, ItemId.RING_OF_WEALTH_I, 100, new Item[]{new Item(ItemId.RING_OF_WEALTH), new Item(ItemId.RING_OF_WEALTH_SCROLL)}),
        J5(UpgradeCategory.JEWELLERY, ItemId.SALVE_AMULETI, 50, new Item[]{new Item(ItemId.SALVE_AMULET)}),
        J6(UpgradeCategory.JEWELLERY, ItemId.SALVE_AMULETEI, 50, new Item[]{new Item(ItemId.SALVE_AMULETI)}),
        J14(UpgradeCategory.JEWELLERY, ItemId.AMULET_OF_RANCOUR_S, 100, new Item[]{new Item(ItemId.AMULET_OF_RANCOUR), new Item(ItemId.HOLY_ORNAMENT_KIT, 1)}),

        // Misc


        ;

        public transient final UpgradeCategory category;
        public final int id;
        public final int chance;

        public final Item[] required;
        public final Item ingredient;

        UpgradableItems(UpgradeCategory category, int id, int chance, Item[] required, Item ingredient) {
            this.category = category;
            this.id = id;
            this.chance = chance;
            this.required = required;
            this.ingredient = ingredient;
        }

    UpgradableItems(UpgradeCategory category, int id, int chance, Item[] required) {
        this.category = category;
        this.id = id;
        this.chance = chance;
        this.required = required;
        this.ingredient = new Item(995, 5000000);
    }
    }