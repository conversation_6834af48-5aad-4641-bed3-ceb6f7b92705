package com.zenyte.game.content.worldevent.highlightedskill;

import com.zenyte.game.content.worldboost.WorldBoost;
import com.zenyte.game.content.worldboost.type.WorldSkillBoost;
import com.zenyte.game.task.TickTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.utils.TimeUnit;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;

import static com.zenyte.game.world.broadcasts.BroadcastType.HIGHLIGHTED_SKILL;
import static com.zenyte.game.world.broadcasts.BroadcastType.LOTTERY;

/**
 * <AUTHOR> (Discord: astra4)
 */
public final class ScheduledHighlightedSkill extends TickTask {

    private final WorldSkillBoost highlightedSkill;

    public ScheduledHighlightedSkill(int ticks, WorldSkillBoost highlightedSkill) {
        this.ticks = ticks;
        this.highlightedSkill = highlightedSkill;
    }

    @Override
    public void run() {
        if (ticks == 0) {

            long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(1);
            WorldBoost worldBoost = new WorldBoost(highlightedSkill, endTime, 1);
            worldBoost.activate(false);

            WorldBroadcasts.sendMessage("<img=21><col=00ff00><shad=000000>New Highlighted Skill: 1.5x XP in " + highlightedSkill.getSkill() + " for 1 hour!", HIGHLIGHTED_SKILL, true);

            // Schedule the next highlighted skill
            int ticks = (int) TimeUnit.HOURS.toTicks(1) + (int) TimeUnit.SECONDS.toTicks(10);
            ScheduledHighlightedSkill scheduler = selectRandomSkillExcept(highlightedSkill, ticks);
            WorldTasksManager.schedule(scheduler, 0, 0);

            stop();
            return;
        }

        if (ticks == TimeUnit.MINUTES.toTicks(5)) {
            WorldBroadcasts.sendMessage("<img=21><col=00FF00><shad=000000>A new highlighted skill will be selected in 5 minutes!", HIGHLIGHTED_SKILL, true);
        }

        ticks--;
    }

    public static ScheduledHighlightedSkill selectRandomSkillExcept(WorldSkillBoost except, int ticks) {
        Set<WorldSkillBoost> skills = EnumSet.copyOf(WorldSkillBoost.SKILLS);
        if (except != null) {
            skills.remove(except);
        }

        WorldSkillBoost skill = Utils.getRandomCollectionElement(skills);
        if (skill == null) {
            throw new IllegalStateException("Unable to select skill; null when trying to generate random skill");
        }

        return new ScheduledHighlightedSkill(ticks, skill);
    }

    public static ScheduledHighlightedSkill selectRandomSkill(int ticks) {
        return selectRandomSkillExcept(null, ticks);
    }

    public long getTicks() {
        return ticks;
    }
}
