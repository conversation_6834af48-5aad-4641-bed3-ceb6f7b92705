package com.zenyte.game.content.skills.firemaking;

import com.zenyte.game.content.essence.utility.impl.DoublePyro;
import com.zenyte.game.content.essence.utility.impl.Pyromaniac;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.player.Action;
import com.zenyte.game.world.entity.player.SkillConstants;

public class BonfireAction extends Action {


    private final Firemaking firemaking;


    public BonfireAction(Firemaking firemaking) {
        this.firemaking = firemaking;
    }

    public boolean check() {
        Firemaking logs = firemaking;
        if(logs != null && logs.getLevel() > player.getSkills().getLevel(SkillConstants.FIREMAKING)) {
            player.sendMessage("You lack the required firemaking level to light these.");
            return false;
        }
        return player.getInventory().containsItem(firemaking.getLogs());
    }

    @Override
    public boolean start() {
        return check();
    }

    @Override
    public boolean process() {
        return check();
    }

    @Override
    public int processWithDelay() {
        int logsToBurn = 1;

        if (player.getUtilityPerkManager().hasPerk(Pyromaniac.class) && Pyromaniac.rollArson() && player.getInventory().getAmountOf(firemaking.getLogs().getId())>1) {
            logsToBurn = player.getInventory().getAmountOf(firemaking.getLogs().getId());
            player.sendFilteredMessage("Your Pyromaniac perk burned all your logs of the type you were burning!");
        }
        else if (player.getUtilityPerkManager().hasPerk(DoublePyro.class) && DoublePyro.roll() && player.getInventory().getAmountOf(firemaking.getLogs().getId())>1) {
            logsToBurn =2;
            player.sendFilteredMessage("Your Double Pyro perk causes you to burn 2 logs instead of 1.");
        }
        player.getInventory().deleteItem(firemaking.getLogs().getId(), logsToBurn);
        player.setAnimation(new Animation(827));

        player.getSkills().addXp(SkillConstants.FIREMAKING, firemaking.getXp() * logsToBurn);
        if(player.getUtilityPerkManager().hasPerk(Pyromaniac.class) && Pyromaniac.rollPage()) {
            player.sendFilteredMessage("Your Pyromancer perk causes a burnt page to arise from the flames.");
            player.getInventory().addOrDrop(ItemId.BURNT_PAGE, 1);
        }

        return 4;
    }

    @Override
    public void stop() {
        delay(3);
    }
}
