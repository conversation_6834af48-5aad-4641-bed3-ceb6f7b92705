package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class DoublePyro extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }

    @Override
    public String name() {
        return "Double Pyro";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_DoublePyro;
    }

    @Override
    public String description() {
        return "20% chance to burn two logs in a single fire or bonfire.";
    }

    @Override
    public int item() {
        return 2946;
    }
}
