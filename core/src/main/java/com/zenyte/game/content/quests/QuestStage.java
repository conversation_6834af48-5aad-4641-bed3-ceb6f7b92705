package com.zenyte.game.content.quests;

import mgi.utilities.StringFormatUtil;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 */
public enum QuestStage {

    NOT_STARTED("Not Started"),
    IN_PROGRESS("In Progress"),
    COMPLETED("Completed");

    private final String displayName;

    public static final QuestStage[] VALUES = values();

    QuestStage(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return StringFormatUtil.formatString(name());
    }
}
