package com.zenyte.game.content.quests;

import com.zenyte.game.content.boss.BossRespawnTimer;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.player.GameCommands;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;
import com.zenyte.plugins.dialogue.OptionsMenuD;

import java.util.ArrayList;
import java.util.List;

/**
 * Quest Commands for administrative quest management
 * 
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 */
public class QuestCommands {

    /**
     * Registers all quest-related commands
     */
    public static void register() {
        new GameCommands.Command(PlayerPrivilege.ADMINISTRATOR, "completequest", "Complete a quest for a player.",
                (p, args) -> openQuestSelector(p, QuestCommandType.COMPLETE));
        new GameCommands.Command(PlayerPrivilege.ADMINISTRATOR, "resetquest", "Resets a quest for a player.",
                (p, args) -> openQuestSelector(p, QuestCommandType.RESET));
        new GameCommands.Command(PlayerPrivilege.ADMINISTRATOR, "setobjective", "Set a specific quest objective for a player.",
                (p, args) -> openQuestSelector(p, QuestCommandType.SET_OBJECTIVE));
        new GameCommands.Command(PlayerPrivilege.ADMINISTRATOR, "completeobjective", "Nearly complete current quest objective for a player to objectiveLength -1.",
                (p, args) -> openQuestSelector(p, QuestCommandType.COMPLETE_OBJECTIVE));
        new GameCommands.Command(PlayerPrivilege.ADMINISTRATOR, "checkquest", "Check quest progress for a player.",
                (p, args) -> openQuestSelector(p, QuestCommandType.CHECK_QUEST));

    }

    /**
     * Opens a quest selector interface for the specified command type
     */
    private static void openQuestSelector(final Player player, final QuestCommandType commandType) {
        final List<String> questTitles = new ArrayList<>();
        final List<Quest[]> questGroups = new ArrayList<>();
        for (final Quest[] questSet : QuestManager.ALL_QUESTS) {
            if (questSet.length > 0) {
                questTitles.add(questSet[0].title());
                questGroups.add(questSet);
            }
        }

        String title;
        switch (commandType) {
            case COMPLETE:
                title = "Complete Quest";
                break;
            case RESET:
                title = "Reset Quest";
                break;
            case SET_OBJECTIVE:
                title = "Set Objective";
                break;
            case COMPLETE_OBJECTIVE:
                title = "Complete Objective";
                break;
            case CHECK_QUEST:
                title = "Check Quest Progress";
                break;
            default:
                title = "Quest Command";
                break;
        }
        
        player.getDialogueManager().start(new OptionsMenuD(player, title, questTitles.toArray(new String[0])) {
            @Override
            public void handleClick(final int slotId) {
                if (slotId < 0 || slotId >= questGroups.size()) {
                    return;
                }
                Quest[] selectedQuestSet = questGroups.get(slotId);
                String questTitle = selectedQuestSet[0].title();
                player.sendInputString("Enter the player name:", playerName -> {
                    Player targetPlayer = World.getPlayer(playerName).orElse(null);
                    if (targetPlayer == null) {
                        player.sendMessage("Player '" + playerName + "' not found or not online.");
                        return;
                    }
                    if (commandType == QuestCommandType.SET_OBJECTIVE) {
                        openObjectiveSelector(player, targetPlayer, questTitle, selectedQuestSet);
                    } else if (commandType == QuestCommandType.COMPLETE_OBJECTIVE) {
                        showCurrentObjectiveCompletion(player, targetPlayer, questTitle, selectedQuestSet);
                    } else if (commandType == QuestCommandType.CHECK_QUEST) {
                        showQuestProgress(player, targetPlayer, questTitle, selectedQuestSet);
                    } else {
                        showConfirmationDialogue(player, targetPlayer, questTitle, selectedQuestSet, commandType);
                    }
                });
            }
        });
    }

    /**
     * Shows confirmation dialogue for quest command
     */
    private static void showConfirmationDialogue(Player admin, Player targetPlayer, String questTitle, 
                                               Quest[] questSet, QuestCommandType commandType) {
        String action = commandType == QuestCommandType.COMPLETE ? "complete" : "reset";
        admin.getDialogueManager().start(new Dialogue(admin) {
            @Override
            public void buildDialogue() {
                options(action.substring(0, 1).toUpperCase() + action.substring(1) + " the quest '" + questTitle + "' for player '" + targetPlayer.getUsername() + "'?", "Yes", "Nevermind")
                        .onOptionOne(() -> executeQuestCommand(admin, targetPlayer, questTitle, questSet, commandType))
                        .onOptionTwo(() -> admin.sendMessage("Quest " + action + " cancelled."));
            }
        });
    }

    /**
     * Executes the quest command (complete or reset)
     */
    private static void executeQuestCommand(Player admin, Player targetPlayer, String questTitle, 
                                          Quest[] questSet, QuestCommandType commandType) {
        try {
            if (commandType == QuestCommandType.COMPLETE) {
                completeQuest(targetPlayer, questTitle, questSet);
                admin.sendMessage("Successfully completed quest '" + questTitle + "' for " + targetPlayer.getUsername());
            } else {
                resetQuest(targetPlayer, questTitle, questSet);
                admin.sendMessage("Successfully reset quest '" + questTitle + "' for " + targetPlayer.getUsername());
            }
        } catch (Exception e) {
            admin.sendMessage("Error executing quest command: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Completes a quest for the target player
     */
    private static void completeQuest(Player player, String questTitle, Quest[] questSet) {
        QuestManager questManager = player.getQuestManager();
        Quest finalObjective = null;
        for (Quest objective : questSet) {
            if (objective.stage() == QuestStage.COMPLETED) {
                finalObjective = objective;
                break;
            }
        }
        if (finalObjective != null) {
            questManager.getCurrentQuestObjectives().put(questTitle, finalObjective.name());
            questManager.finish(finalObjective);
        } else {
            questManager.setFinished(questSet[0]);
        }
        if (questSet.length > 0 && questSet[0] instanceof QuestCommandHandler) {
            ((QuestCommandHandler) questSet[0]).onQuestComplete(player);
        }
        player.sendMessage("<col=00ff00>Quest '" + questTitle + "' has been completed by an admin!</col>");
    }

    /**
     * Resets a quest for the target player
     */
    private static void resetQuest(Player player, String questTitle, Quest[] questSet) {
        QuestManager questManager = player.getQuestManager();
        if (questSet.length > 0) {
            questManager.reset(questSet[0]);
        }
        Quest firstObjective = null;
        for (Quest objective : questSet) {
            if (objective.stage() == QuestStage.NOT_STARTED) {
                firstObjective = objective;
                break;
            }
        }
        if (firstObjective != null) {
            questManager.startQuest(firstObjective);
        }
        if (questSet.length > 0 && questSet[0] instanceof QuestCommandHandler) {
            ((QuestCommandHandler) questSet[0]).onQuestReset(player);
        }
        player.sendMessage("<col=ff0000>Quest '" + questTitle + "' has been reset by an admin!</col>");
    }

    /**
     * Opens an objective selector for setting a specific objective
     */
    private static void openObjectiveSelector(Player admin, Player targetPlayer, String questTitle, Quest[] questSet) {
        final List<String> objectiveNames = new ArrayList<>();
        final List<Quest> objectives = new ArrayList<>();
        for (Quest objective : questSet) {
            String display = QuestUtil.getQuestObjectiveDisplay(targetPlayer, objective);
            objectiveNames.add(display);
            objectives.add(objective);
        }
        admin.getDialogueManager().start(new OptionsMenuD(admin, "Set Objective for " + questTitle, objectiveNames.toArray(new String[0])) {
            @Override
            public void handleClick(final int slotId) {
                if (slotId < 0 || slotId >= objectives.size()) {
                    return;
                }
                Quest selectedObjective = objectives.get(slotId);
                admin.getDialogueManager().start(new Dialogue(admin) {
                    @Override
                    public void buildDialogue() {
                        options("Set objective '" + selectedObjective.name() + "' for player '" + targetPlayer.getUsername() + "'?", "Yes", "Nevermind")
                                .onOptionOne(() -> executeSetObjective(admin, targetPlayer, questTitle, selectedObjective))
                                .onOptionTwo(() -> admin.sendMessage("Set objective cancelled."));
                    }
                });
            }
        });
    }

    /**
     * Shows current objective completion dialogue
     */
    private static void showCurrentObjectiveCompletion(Player admin, Player targetPlayer, String questTitle, Quest[] questSet) {
        String currentObjectiveName = targetPlayer.getQuestManager().getCurrentObjective(questTitle);

        if (currentObjectiveName == null) {
            admin.sendMessage("'" + targetPlayer.getUsername() + "' has no current objective for quest '" + questTitle + "'.");
            return;
        }
        Quest currentObjective = null;
        for (Quest objective : questSet) {
            if (objective.name().equals(currentObjectiveName)) {
                currentObjective = objective;
                break;
            }
        }
        if (currentObjective == null) {
            admin.sendMessage("Current objective '" + currentObjectiveName + "' not found in quest.");
            return;
        }
        final Quest finalCurrentObjective = currentObjective;
        String objectiveText = QuestUtil.getQuestObjectiveDisplay(targetPlayer, currentObjective);
        admin.getDialogueManager().start(new Dialogue(admin) {
            @Override
            public void buildDialogue() {
                options("Complete '" + objectiveText + "' for player '" + targetPlayer.getUsername() + "'?", "Yes", "Nevermind")
                        .onOptionOne(() -> executeCompleteObjective(admin, targetPlayer, questTitle, finalCurrentObjective))
                        .onOptionTwo(() -> admin.sendMessage("Complete objective cancelled."));
            }
        });
    }

    /**
     * Executes setting a specific objective for a player
     */
    private static void executeSetObjective(Player admin, Player targetPlayer, String questTitle, Quest objective) {
        try {
            QuestManager questManager = targetPlayer.getQuestManager();

            // First reset the quest to clear all state
            questManager.reset(objective);

            // Then start the quest at the specific objective
            questManager.startQuest(objective);

            admin.sendMessage("Successfully set objective '" + objective.name() + "' for " + targetPlayer.getUsername());
            targetPlayer.sendMessage("An admin has set your quest objective to: " + String.join(" ", objective.objectiveDescription()));
        } catch (Exception e) {
            admin.sendMessage("Error setting objective: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Executes completing the current objective for a player
     */
    private static void executeCompleteObjective(Player admin, Player targetPlayer, String questTitle, Quest objective) {
        try {
            QuestManager questManager = targetPlayer.getQuestManager();
            handleQuestObjectiveVariables(targetPlayer, objective);
            int currentProgress = questManager.getProgress(objective);
            int targetProgress = objective.objectiveLength() - 1; //-1 so the player still gets the rewards and complete dialogue as if they completed the objective
            int progressToAdd = Math.max(0, targetProgress - currentProgress);
            if (progressToAdd > 0) {
                questManager.update(objective, progressToAdd, true);
            }
            admin.sendMessage("Successfully set objective '" + objective.name() + "' to completion state for " + targetPlayer.getUsername());
            targetPlayer.sendMessage("An admin has set your quest objective to completion: " + String.join(" ", objective.objectiveDescription()));
        } catch (Exception e) {
            admin.sendMessage("Error completing objective: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handles special combat perk objectives when completing them via admin command
     */
    private static void handleQuestObjectiveVariables(Player player, Quest objective) {
        String objectiveName = objective.name();

        switch (objectiveName) {
            case "UNLOCK_MAGIC_COMBAT_SPEED_PERK":
                player.getCombatPerkManager().unlockedPerks.add("MagicISpeed");
                player.getVarManager().sendBitInstant(19699 + 7, 1); // Magic
                player.getVarManager().sendBitInstant(19698, 1);
                player.sendMessage("<col=00ff00>Magic I Combat Speed Perk unlocked!</col>");
                break;

            case "UNLOCK_RANGED_COMBAT_SPEED_PERK":
                player.getCombatPerkManager().unlockedPerks.add("RangedISpeed");
                player.getVarManager().sendBitInstant(19699 + 4, 1); // Ranged
                player.getVarManager().sendBitInstant(19698, 1);
                player.sendMessage("<col=00ff00>Ranged I Combat Speed Perk unlocked!</col>");
                break;

            case "UNLOCK_MELEE_COMBAT_SPEED_PERK":
                player.getCombatPerkManager().unlockedPerks.add("MeleeISpeed");
                player.getVarManager().sendBitInstant(19699 + 1, 1); // Melee
                player.getVarManager().sendBitInstant(19698, 1);
                player.sendMessage("<col=00ff00>Melee I Combat Speed Perk unlocked!</col>");
                break;

            default:
                // No special handling needed for other objectives
                break;
        }
    }

    /**
     * Shows quest progress for a player (read-only, no clicking allowed)
     */
    private static void showQuestProgress(Player admin, Player targetPlayer, String questTitle, Quest[] questSet) {
        final List<String> objectiveStatus = new ArrayList<>();

        // Collect all objectives with their completion status
        for (Quest objective : questSet) {
            boolean isCompleted = targetPlayer.getQuestManager().isCompleted(objective);
            int currentProgress = targetPlayer.getQuestManager().getProgress(objective);
            int maxProgress = objective.objectiveLength();
            String currentObjective = targetPlayer.getQuestManager().getCurrentObjective(questTitle);
            boolean isCurrent = currentObjective != null && currentObjective.equals(objective.name());

            String status;
            if (isCompleted) {
                status = "<col=00ff00>[COMPLETED]</col>";
            } else if (isCurrent) {
                status = "<col=ffff00>[CURRENT " + currentProgress + "/" + maxProgress + "]</col>";
            } else if (currentProgress > 0) {
                status = "<col=ff8000>[PARTIAL " + currentProgress + "/" + maxProgress + "]</col>";
            } else {
                status = "<col=ff0000>[NOT STARTED]</col>";
            }

            String display = status + " " + String.join(" ", objective.objectiveDescription());
            objectiveStatus.add(display);
        }

        // Show in a non-clickable interface using OptionsMenuD but override handleClick to do nothing
        admin.getDialogueManager().start(new OptionsMenuD(admin, "Quest Progress: " + questTitle + " (" + targetPlayer.getUsername() + ")", objectiveStatus.toArray(new String[0])) {
            @Override
            public void handleClick(final int slotId) {
                // Do nothing - this is read-only
                admin.sendMessage("This is a read-only quest progress view.");
            }

            @Override
            public boolean cancelOption() {
                return true; // Allow canceling
            }
        });
    }

    /**
     * Enum for quest command types
     */
    private enum QuestCommandType {
        COMPLETE,
        RESET,
        SET_OBJECTIVE,
        COMPLETE_OBJECTIVE,
        CHECK_QUEST
    }

    /**
     * Interface for quests that need custom handling during complete/reset commands
     */
    public interface QuestCommandHandler {
        /**
         * Called when quest is completed via admin command
         * Use this to set any quest-specific variables or cleanup
         */
        void onQuestComplete(Player player);
        
        /**
         * Called when quest is reset via admin command  
         * Use this to reset any quest-specific variables or cleanup
         */
        void onQuestReset(Player player);
    }
}
