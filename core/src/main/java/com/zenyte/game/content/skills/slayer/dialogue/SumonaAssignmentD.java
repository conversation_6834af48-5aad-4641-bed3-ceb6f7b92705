package com.zenyte.game.content.skills.slayer.dialogue;

import com.zenyte.game.content.skills.slayer.*;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.region.GlobalAreaManager;
import com.zenyte.game.world.region.RegionArea;

public final class SumonaAssignmentD extends Dialogue {

	public SumonaAssignmentD(final Player player, final NPC npc) {
		super(player, npc);
	}

	private Assignment task;

	@Override
	public void buildDialogue() {
		final SlayerMaster master = SlayerMaster.mappedMasters.get(npc.getId());
		if (master == null) {
			return;
		}

		final Slayer slayer = player.getSlayer();
		final Assignment currentTask = slayer.getAssignment();
		if (currentTask != null) {
			final Class<? extends RegionArea> clazz = currentTask.getArea();
			if (master.equals(SlayerMaster.KONAR_QUO_MATEN) && clazz != null) {
				final RegionArea area = GlobalAreaManager.getArea(clazz);
				npc("You're still bringing balance to " + currentTask.getTask().toString().toLowerCase() + " in the " + area.name() + ", with " + currentTask.getAmount() + " to go. Come back when you're finished.");
			} else {
				npc("You're still hunting " + currentTask.getTask().toString().toLowerCase() + "; you have " + currentTask.getAmount() + " to go. Come back when you've finished your task.");
			}
			return;
		}

		final Skills skills = player.getSkills();
		if (skills.getLevel(SkillConstants.SLAYER) < SlayerMaster.SUMONA.getSlayerRequirement() || skills.getCombatLevel() < SlayerMaster.SUMONA.getCombatRequirement()) {
			npc("Sorry, but you're not skilled enough to be taught by<br><br>me. Your best trainer would be " + player.getSlayer().getAdvisedMaster() + ".");
			return;
		}

		if(!slayer.isUnlocked("Like a boss")) {
			npc("You must unlock 'Like a Boss' before I can assign you boss tasks!");
			return;
		}

		task = slayer.generateSummonaTask();
		slayer.setAssignment(task); //Set default task in case they click away while selecting amount
		npc("You're currently assigned to kill " + task.getTask().toString() + ". How many would you like to slay?").executeAction(() -> {
			finish();
			player.sendInputInt("How many would you like to slay? (3 - 35)", value -> {
				final int num = Math.max(Math.min(value, 35), 3);
				task.setAmount(num);
				task.setInitialAmount(num);
				slayer.setAssignment(task);
				player.getDialogueManager().start(new BossAssignmentExtension(player, npc, task));
			});
		});
	}

	public static final class BossAssignmentExtension extends Dialogue {
		public BossAssignmentExtension(final Player player, final NPC npc, final Assignment assignment) {
			super(player, npc);
			this.assignment = assignment;
		}

		private final Assignment assignment;

		@Override
		public void buildDialogue() {
			final Slayer slayer = player.getSlayer();
			if (slayer.getMaster() != SlayerMaster.SUMONA) {
				slayer.setMaster(SlayerMaster.SUMONA);
			}
			player.getSlayer().setAssignment(assignment);
			npc("Your new task is to kill " + assignment.getAmount() + " " + assignment.getTask().toString() + ".");
			options(TITLE, "Got any good tips or teleports that would help?", "Okay, great!").onOptionOne(() -> setKey(100));
			player(100, "Got any good tips or teleports that would help?").executeAction(() -> {
				finish();
				SlayerTipsAndTeleports.showSlayerTips(player, npc);
			});
		}
	}
}
