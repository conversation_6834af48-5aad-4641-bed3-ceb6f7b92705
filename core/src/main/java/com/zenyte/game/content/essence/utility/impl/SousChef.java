package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;

public class SousChef extends EssencePerk {
    @Override
    public String name() {
        return "Sous Chef";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SousChef;
    }

    @Override
    public String description() {
        return "Decreases cooking speed by 1 tick and prevents burning.";
    }

    @Override
    public int item() {
        return 7441;
    }
}
