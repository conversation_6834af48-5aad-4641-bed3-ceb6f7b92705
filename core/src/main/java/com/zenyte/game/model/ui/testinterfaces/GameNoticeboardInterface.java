package com.zenyte.game.model.ui.testinterfaces;

import com.google.common.eventbus.Subscribe;
import com.near_reality.api.service.user.UserPlayerAttributesKt;
import com.near_reality.api.service.vote.VotePlayerAttributesKt;
import com.zenyte.game.GameClock;
import com.zenyte.game.GameConstants;
import com.zenyte.game.GameInterface;
import com.zenyte.game.content.achievementdiary.Diary;
import com.zenyte.game.content.donation.DonationToggle;
import com.zenyte.game.model.BonusXpManager;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.model.ui.InterfaceHandler;
import com.zenyte.game.model.ui.PaneType;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.player.*;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;
import com.zenyte.plugins.events.LoginEvent;
import com.zenyte.plugins.events.LogoutEvent;
import com.zenyte.utils.TimeUnit;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zenyte.game.model.ui.InterfaceHandler.Journal.GAME_NOTICEBOARD;
import static com.zenyte.game.model.ui.InterfaceHandler.Journal.SERVER_EVENTS;

/**
 * <AUTHOR> | 2-12-2018 | 16:05
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public final class GameNoticeboardInterface extends Interface {
    private static final Logger logger = LoggerFactory.getLogger(GameNoticeboardInterface.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("dd MMM yyyy");
    private static final PlayerPrivilege[] STAFF = {
            PlayerPrivilege.OWNER, PlayerPrivilege.DEVELOPER, PlayerPrivilege.ADMINISTRATOR,
            PlayerPrivilege.SENIOR_MODERATOR, PlayerPrivilege.MODERATOR, PlayerPrivilege.SUPPORT,
    };

    @Subscribe
    public static void onLogin(final LoginEvent event) {
        final Player p = event.getPlayer();
        //Frame, Uptime, Time played, personal bonus xp, global bonus xp, raids boost
        p.getPacketDispatcher().sendClientScript(10586, 162 << 16 | 2, 1701 << 16 | 7, 1701 << 16 | 16, 1701 << 16 | 26, 1701 << 16 | 25, 1701 << 16 | 33, 1701 << 16 | 47);
        p.getPacketDispatcher().sendClientScript(10587, 1701 << 16 | 5, 1701 << 16 | 6);
        p.getVariables().synchNoticeboardVars();
        GameInterface.GAME_NOTICEBOARD.getPlugin().ifPresent(plugin ->
        p.getPacketDispatcher().sendComponentText(plugin.getInterface(), plugin.getComponent("Time"), "Time: " + Colour.WHITE.wrap(GameClock.gameTime())));
        WorldTasksManager.schedule(GameNoticeboardInterface::refreshCounters, 1);
    }

    public static void refreshXericsWisdom(@NotNull final Player player) {
        player.getVarManager().sendVar(3806, (int) (player.getVariables().getRaidsBoost() * 0.6F));
    }

    public static void refreshBonusXP() {
        final int bonusXPSecondsLeft = Math.max(0, (int) TimeUnit.MILLISECONDS.toSeconds(
                BonusXpManager.expirationDate - System.currentTimeMillis())
        );
        for (final Player player : World.getPlayers()) {
            player.getVarManager().sendVar(3805, bonusXPSecondsLeft);
        }
    }

    public static void refreshCounters() {
        for (final Player player : World.getPlayers()) {
            if (player == null) continue;
            if (player.getInterfaceHandler().getJournal() != GAME_NOTICEBOARD) //If they aren't viewing the noticeboard, skip them since it will reload if they open the interface again.
                continue;
            player.getVarManager().sendVarInstant(3803, World.getDisplayedPlayerCount());
            player.getVarManager().sendVarInstant(3804, World.getStaffCountOnline());
            player.getPacketDispatcher().sendClientScript(10587, 1701 << 16 | 5, 1701 << 16 | 6); // Trigger the client script to refresh the interface display
        }
    }

    @Subscribe
    public static void onLogout(final LogoutEvent event) {
        WorldTasksManager.schedule(GameNoticeboardInterface::refreshCounters, 1);
        final Player p = event.getPlayer();
    }

    private static List<Player> getStaff(final Player requester, final PlayerPrivilege privilege) {
        final boolean requesterIsStaff = requester.getPrivilege().inherits(PlayerPrivilege.FORUM_MODERATOR);
        return World.getPlayers().stream().filter(p -> privilege.equals(p.getPrivilege()) && (requesterIsStaff || !isHidden(p))).collect(Collectors.toList());
    }

    public static void showStaffOnline(final Player player) {
        final ArrayList<String> lines = new ArrayList<>();
        int count = 0;
        for (final PlayerPrivilege privilege : STAFF) {
            final List<Player> members = getStaff(player, privilege);
            final int size = members.size();
            if (size < 1) continue;
            count += size;
            lines.add(privilege.crown().getCrownTag() + ' ' + Colour.DARK_BLUE.wrap(privilege.getPrettyName() + 's'));
            members.forEach(p -> lines.add(p.getName().replace('_', ' ') + (isHidden(p) ? " (" + Colour.MAROON.wrap("Hidden") + ")" : "")));
            lines.add("\n");
        }
        Diary.sendJournal(player, "Staff online: " + count, lines);
    }

    public static boolean isHidden(final Player player) {
        return !SocialManager.PrivateStatus.ALL.equals(player.getSocialManager().getStatus());
    }

    @Override
    protected void attach() {
        put(5, "Players Online");
        put(6, "Staff Online");
        put(7, "Uptime");
        put(8, "Time");

        put(10, "XP Rate");
        put(11, "Mode");
        put(12, "Total Donated");
        put(13, "Drop Rate");
        put(14, "Loyalty Points");
        put(15, "Store Credits");
        put(41, "Donator Rank");
        put(16, "Vote Points");

        put(19, "Drop Viewer");
        put(21, "Utility Tasks");
        put(23, "Premium Toggles");

        put(25, "Global Bonus XP");
        put(26, "Personal Bonus XP");
        put(27, "Check Boosters");

        put(30, "Website");
        put(32, "Discord");
        put(34, "Store");
        put(36, "Vote");
    }

    @Override
    public void open(Player player) {
        player.getInterfaceHandler().sendInterface(getInterface().getId(), 33, PaneType.JOURNAL_TAB_HEADER, true);
        player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("Time"), "Time: <col=ffffff>" + GameClock.gameTime());
        //player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("2FA"), (player.getAuthenticator().isEnabled() ? Colour.GREEN : Colour.RED).wrap("Two-Factor Authentication"));
        player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("XP Rate"), "XP Rate: <col=ffffff>" + ((player.getSkillingXPRate() == 1) ? "-" : (player.getSkillingXPRate() + "x</col>")));
        player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("Mode"), "Mode: <col=ffffff>" + player.getGameModeCrown().getCrownTag() + player.getGameMode().toString() + "</col>");
        player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("Vote Points"), "Vote Points: <col=ffffff>" + VotePlayerAttributesKt.getTotalVotePoints(player) + "</col>");
        player.getPacketDispatcher().sendComponentText(getInterface(), getComponent("Loyalty Points"), "Loyalty Points: <col=ffffff>" + player.getLoyaltyManager().getLoyaltyPoints() + "</col>");
        //player.getPacketDispatcher().sendComponentSettings(getInterface(), getComponent("2FA"), -1, 0, AccessMask.CLICK_OP1);
        GameNoticeboardInterface.updateDropRate(player, true);
        refreshCounters();
        updateDonatorData(player);
    }
    public static void updateDonatorData(Player player) {
        final Optional<Interface> optionalPlugin = GameInterface.GAME_NOTICEBOARD.getPlugin();
        if (optionalPlugin.isPresent()) {
            final Interface plugin = optionalPlugin.get();
            player.getPacketDispatcher().sendComponentText(plugin.getInterface(), plugin.getComponent("Donator Rank"), "Donator Rank: <shad=000000><col=" + player.getMemberRank().getYellColor() + ">" + player.getMemberName() + "</col></shad>");
            player.getPacketDispatcher().sendComponentText(plugin.getInterface(), plugin.getComponent("Total Donated"), "Total Donated: <col=ffffff>$" + UserPlayerAttributesKt.getStoreTotalSpent(player) + "</col>");
            player.getPacketDispatcher().sendComponentText(plugin.getInterface(), plugin.getComponent("Store Credits"), "Store Credits: <col=ffffff>" + UserPlayerAttributesKt.getStoreCredits(player) + "</col>");
        }
    }

    public static void updateDropRate(Player player, boolean updateNoticeboard) {
        int percent = (int) player.getDropRateBonus();

        if (updateNoticeboard) {// Only update the noticeboard if requested
            final Optional<Interface> optionalPlugin = GameInterface.GAME_NOTICEBOARD.getPlugin();
            optionalPlugin.ifPresent(plugin -> player.getPacketDispatcher().sendComponentText(plugin.getInterface(), plugin.getComponent("Drop Rate"), "Drop Rate: " + Colour.WHITE.wrap(percent + "%")));
        }
    }

    @Override
    protected void build() {
        bind("Staff Online", GameNoticeboardInterface::showStaffOnline);
        bind("Drop Viewer", player -> {
            player.getDialogueManager().start(new Dialogue(player) {
                @Override
                public void buildDialogue() {
                    options("Which viewer would you like to open?", "NPC / Item Drops", "Chest / Box Drops")
                            .onOptionOne(() -> GameInterface.DROP_VIEWER.open(player))
                            .onOptionTwo(() -> DropViewerInterface.openToAlternateTables(player));
                }
            });
        });
        bind("Utility Tasks", GameInterface.UTILITY_TASKS::open);
        bind("Website", player -> player.getPacketDispatcher().sendURL(GameConstants.SERVER_WEBSITE_URL));
        bind("Discord", player -> player.getPacketDispatcher().sendURL(GameConstants.DISCORD_INVITE));
        bind("Store", GameInterface.CREDIT_STORE::open);
        bind("Vote", GameInterface.VOTE::open);
        bind("Premium Toggles", player -> {
            if(player.isMember()) {
                DonationToggle.openInterface(player);
            } else {
                player.sendMessage("You need to be a donator to open this.");
            }
        });
        bind("Check Boosters", GameCommands::openBoosters);
    }

    @Override
    public GameInterface getInterface() {
        return GameInterface.GAME_NOTICEBOARD;
    }
}
