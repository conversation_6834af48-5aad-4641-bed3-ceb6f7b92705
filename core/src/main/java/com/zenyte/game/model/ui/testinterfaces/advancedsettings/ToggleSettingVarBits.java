package com.zenyte.game.model.ui.testinterfaces.advancedsettings;

import static com.zenyte.game.model.ui.testinterfaces.advancedsettings.SettingStructs.*;
import static com.zenyte.game.model.ui.testinterfaces.advancedsettings.SettingVariables.*;

/**
 * <AUTHOR>
 */
public enum ToggleSettingVarBits implements SettingStructResolvable {

    HIDE_ROOFS(HIDE_ROOFS_STRUCT_ID, HIDE_ROOFS_VARBIT_ID),
    MUSIC_UNLOCK_MESSAGE(MUSIC_UNLOCK_MESSAGE_STRUCT_ID, MUSIC_UNLOCK_MESSAGE_VARBIT_ID),
    HITSPLAT_TINTING(HIT<PERSON>LAT_TINTING_STRUCT_ID, HITSPLAT_TINTING_VARBIT_ID, 1),
    SHOW_WIKI_ENTITY_LOOKUP(SHOW_WIKI_ENTITY_LOOKUP_STRUCT_ID, SHOW_WIKI_ENTITY_LOOKUP_VARBIT_ID, 1),
    SHOW_DATA_ORBS(SHOW_DATA_ORBS_STRUCT_ID, SHOW_DATA_ORBS_VARBIT_ID, 1),
    TRANSPARENT_SIDE_PANEL(TRANSPARENT_SIDE_PANEL_STRUCT_ID, TRANSPARENT_SIDE_PANEL_VARBIT_ID),
    SHOW_THE_REMAINING_XP_FOR_A_LEVEL_IN_THE_STATS_PANEL(SHOW_THE_REMAINING_XP_FOR_A_LEVEL_IN_THE_STATS_PANEL_STRUCT_ID, SHOW_THE_REMAINING_XP_FOR_A_LEVEL_IN_THE_STATS_PANEL_VARBIT_ID),
    SHOW_PRAYER_TOOLTIPS(SHOW_PRAYER_TOOLTIPS_STRUCT_ID, SHOW_PRAYER_TOOLTIPS_VARBIT_ID),
    SHOW_SPECIAL_ATTACK_TOOLTIP(SHOW_SPECIAL_ATTACK_TOOLTIP_STRUCT_ID, SHOW_SPECIAL_ATTACK_TOOLTIP_VARBIT_ID),
    SHOW_BOSS_HEALTH_OVERLAY(SHOW_BOSS_HEALTH_OVERLAY_STRUCT_ID, SHOW_BOSS_HEALTH_OVERLAY_VARBIT_ID),
    SHOW_NORMAL_HEALTH_OVERLAY(SHOW_NORMAL_HEALTH_OVERLAY_STRUCT_ID, SHOW_NORMAL_HEALTH_OVERLAY_VARBIT_ID),
    TRANSPARENT_CHATBOX(TRANSPARENT_CHATBOX_STRUCT_ID, TRANSPARENT_CHATBOX_VARBIT_ID),
    SCROLL_WHEEL_CAN_CHANGE_ZOOM_DISTANCE(SCROLL_WHEEL_CAN_CHANGE_ZOOM_DISTANCE_STRUCT_ID,
            SCROLL_WHEEL_CAN_CHANGE_ZOOM_DISTANCE_VARBIT_ID, 1),
    HIDE_PRIVATE_CHAT_WHEN_THE_CHATBOX_IS_HIDDEN(HIDE_PRIVATE_CHAT_WHEN_THE_CHATBOX_IS_HIDDEN_STRUCT_ID,
            HIDE_PRIVATE_CHAT_WHEN_THE_CHATBOX_IS_HIDDEN_VARBIT_ID),
    LOOT_DROP_NOTIFICATIONS(LOOT_DROP_NOTIFICATIONS_STRUCT_ID, LOOT_DROP_NOTIFICATIONS_VARBIT_ID),
    UNTRADEABLE_LOOT_NOTIFICATIONS(UNTRADEABLE_LOOT_NOTIFICATIONS_STRUCT_ID, UNTRADEABLE_LOOT_NOTIFICATIONS_VARBIT_ID),
    FILTER_OUT_BOSS_KILLCOUNT_WITH_SPAMFILTER(FILTER_OUT_BOSS_KILLCOUNT_WITH_SPAMFILTER_STRUCT_ID,
            FILTER_OUT_BOSS_KILLCOUNT_WITH_SPAMFILTER_VARBIT_ID),
    DROP_ITEM_WARNING(DROP_ITEM_WARNING_STRUCT_ID, DROP_ITEM_WARNING_VARBIT_ID),
    CLICK_THROUGH_TRANSPARENT_CHATBOX(CLICK_THROUGH_TRANSPARENT_CHATBOX_STRUCT_ID,
            CLICK_THROUGH_TRANSPARENT_CHATBOX_VARBIT_ID),
    SHOW_THE_STORE_BUTTON_ON_MOBILE(SHOW_THE_STORE_BUTTON_ON_MOBILE_STRUCT_ID,
            SHOW_THE_STORE_BUTTON_ON_MOBILE_VARBIT_ID),
    SHOW_THE_STORE_BUTTON_ON_DESKTOP(SHOW_THE_STORE_BUTTON_ON_DESKTOP_STRUCT_ID,
            SHOW_THE_STORE_BUTTON_ON_DESKTOP_VARBIT_ID, 1),
    MIDDLE_MOUSE_BUTTON_CONTROLS_THE_CAMERA(MIDDLE_MOUSE_BUTTON_CONTROLS_THE_CAMERA_STRUCT_ID,
            MIDDLE_MOUSE_BUTTON_CONTROLS_THE_CAMERA_VARBIT_ID),
    MOVE_FOLLOWER_OPTIONS_LOWER_DOWN(MOVE_FOLLOWER_OPTIONS_LOWER_DOWN_STRUCT_ID,
            MOVE_FOLLOWER_OPTIONS_LOWER_DOWN_VARBIT_ID),
    SHIFT_CLICK_TO_DROP_ITEMS(SHIFT_CLICK_TO_DROP_ITEMS_STRUCT_ID, SHIFT_CLICK_TO_DROP_ITEMS_VARBIT_ID, 1),
    SHOW_THE_FUNCTION_BUTTON(SHOW_THE_FUNCTION_BUTTON_STRUCT_ID, SHOW_THE_FUNCTION_BUTTON_VARBIT_ID),
    MODERN_LAYOUT_SIDE_PANEL_CAN_BE_CLOSED_BY_THE_HOTKEYS(MODERN_LAYOUT_SIDE_PANEL_CAN_BE_CLOSED_BY_THE_HOTKEYS_STRUCT_ID, MODERN_LAYOUT_SIDE_PANEL_CAN_BE_CLOSED_BY_THE_HOTKEYS_VARBIT_ID),
    ESC_CLOSES_THE_CURRENT_INTERFACE(ESC_CLOSES_THE_CURRENT_INTERFACE_STRUCT_ID,
            ESC_CLOSES_THE_CURRENT_INTERFACE_VARBIT_ID, 0),
    ACCEPT_AID(ACCEPT_AID_STRUCT_ID, ACCEPT_AID_VARBIT_ID, 1),
    SHOW_WARNING_WHEN_CASTING_TELEPORT_TO_TARGET(SHOW_WARNING_WHEN_CASTING_TELEPORT_TO_TARGET_STRUCT_ID,
            SHOW_WARNING_WHEN_CASTING_TELEPORT_TO_TARGET_VARBIT_ID),
    SHOW_WARNING_WHEN_CASTING_DAREEYAK_TELEPORT(SHOW_WARNING_WHEN_CASTING_DAREEYAK_TELEPORT_STRUCT_ID,
            SHOW_WARNING_WHEN_CASTING_DAREEYAK_TELEPORT_VARBIT_ID),
    SHOW_WARNING_WHEN_CASTING_CARRALLANGAR_TELEPORT(SHOW_WARNING_WHEN_CASTING_CARRALLANGAR_TELEPORT_STRUCT_ID,
            SHOW_WARNING_WHEN_CASTING_CARRALLANGAR_TELEPORT_VARBIT_ID),
    SHOW_WARNING_WHEN_CASTING_ANNAKARL_TELEPORT(SHOW_WARNING_WHEN_CASTING_ANNAKARL_TELEPORT_STRUCT_ID,
            SHOW_WARNING_WHEN_CASTING_ANNAKARL_TELEPORT_VARBIT_ID),
    SHOW_WARNING_WHEN_CASTING_GHORROCK_TELEPORT(SHOW_WARNING_WHEN_CASTING_GHORROCK_TELEPORT_STRUCT_ID,
            SHOW_WARNING_WHEN_CASTING_GHORROCK_TELEPORT_VARBIT_ID),
    CASTING_ALCHEMY_SPELLS_ON_UNTRADEABLE_ITEMS_ALWAYS_TRIGGERS_A_WARNING(CASTING_ALCHEMY_SPELLS_ON_UNTRADEABLE_ITEMS_ALWAYS_TRIGGERS_A_WARNING_STRUCT_ID, CASTING_ALCHEMY_SPELLS_ON_UNTRADEABLE_ITEMS_ALWAYS_TRIGGERS_A_WARNING_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_ICE_PLATEAU(SHOW_WARNING_WHEN_USING_TABLET_ICE_PLATEAU_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_ICE_PLATEAU_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_CEMETERY(SHOW_WARNING_WHEN_USING_TABLET_CEMETERY_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_CEMETERY_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_WILDERNESS_CRABS(SHOW_WARNING_WHEN_USING_TABLET_WILDERNESS_CRABS_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_WILDERNESS_CRABS_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_DAREEYAK(SHOW_WARNING_WHEN_USING_TABLET_DAREEYAK_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_DAREEYAK_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_CARRALLANGAR(SHOW_WARNING_WHEN_USING_TABLET_CARRALLANGAR_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_CARRALLANGAR_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_ANNAKARL(SHOW_WARNING_WHEN_USING_TABLET_ANNAKARL_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_ANNAKARL_VARBIT_ID),
    SHOW_WARNING_WHEN_USING_TABLET_GHORROCK(SHOW_WARNING_WHEN_USING_TABLET_GHORROCK_STRUCT_ID,
            SHOW_WARNING_WHEN_USING_TABLET_GHORROCK_VARBIT_ID),
    ENABLE_PRECISE_TIMING(ENABLE_PRECISE_TIMING_STRUCT_ID, ENABLE_PRECISE_TIMING_VARBIT_ID),
    ENABLE_SEPARATING_HOURS(ENABLE_SEPARATING_HOURS_STRUCT_ID, ENABLE_SEPARATING_HOURS_VARBIT_ID),
    FOOD_AND_POTIONS_CAN_FORM_SUPPLY_PILES_ON_DEATH(FOOD_AND_POTIONS_CAN_FORM_SUPPLY_PILES_ON_DEATH_STRUCT_ID,
            FOODPOTIONS_CAN_FORM_SUPPLY_PILES_ON_DEATH_VARBIT_ID),
    TILE_HIGHLIGHTING(TILE_HIGHLIGHTING_STRUCT_ID, TILE_HIGHLIGHTING_VARBIT_ID),
    SHOW_MOUSEOVER_TOOLTIPS(SHOW_MOUSEOVER_TOOLTIPS_STRUCT_ID, SHOW_MOUSEOVER_TOOLTIPS_VARBIT_ID),
    SHOW_MOUSEOVER_TEXT(SHOW_MOUSEOVER_TEXT_STRUCT_ID, SHOW_MOUSEOVER_TEXT_VARBIT_ID),
    DATA_ORBS_REGENERATION_INDICATORS(DATA_ORBS_REGENERATION_INDICATORS_STRUCT_ID,
            DATA_ORBS_REGENERATION_INDICATORS_VARBIT_ID, 1),
    CHAMBERS_OF_XERIC_HELPER(CHAMBERS_OF_XERIC_HELPER_STRUCT_ID, CHAMBERS_OF_XERIC_HELPER_VARBIT_ID),
    AGILITY_HELPER(AGILITY_HELPER_STRUCT_ID, AGILITY_HELPER_VARBIT_ID),
    HIGHLIGHT_AGILITY_OBSTACLES(HIGHLIGHT_AGILITY_OBSTACLES_STRUCT_ID, HIGHLIGHT_AGILITY_OBSTACLES_VARBIT_ID),
    HIGHLIGHT_AGILITY_SHORTCUTS(HIGHLIGHT_AGILITY_SHORTCUTS_STRUCT_ID, HIGHLIGHT_AGILITY_SHORTCUTS_VARBIT_ID),
    FISHING_SPOT_INDICATORS(FISHING_SPOT_INDICATORS_STRUCT_ID, FISHING_SPOT_INDICATORS_VARBIT_ID),
    FISHING_SPOT_INDICATORS_TOOLS_ONLY(FISHING_SPOT_INDICATORS_TOOLS_ONLY_STRUCT_ID,
            FISHING_SPOT_INDICATORS_TOOLS_ONLY_VARBIT_ID),
    FISHING_SPOT_INDICATORS_MOUSE_OVER_TOOLTIP(FISHING_SPOT_INDICATORS_MOUSE_OVER_TOOLTIP_STRUCT_ID,
            FISHING_SPOT_INDICATORS_MOUSE_OVER_TOOLTIP_VARBIT_ID),
    SHOW_ATTACK_STYLE(SHOW_ATTACK_STYLE_STRUCT_ID, SHOW_ATTACK_STYLE_VARBIT_ID),
    HOME_TELEPORT_COOLDOWN(HOME_TELEPORT_COOLDOWN_STRUCT_ID, HOME_TELEPORT_COOLDOWN_VARBIT_ID),
    MINIGAME_TELEPORT_COOLDOWN(MINIGAME_TELEPORT_COOLDOWN_STRUCT_ID, MINIGAME_TELEPORT_COOLDOWN_VARBIT_ID),
    DISPLAY_BUFF_BAR(DISPLAY_BUFF_BAR_STRUCT_ID, DISPLAY_BUFF_BAR_VARBIT_ID),
    DISPLAY_FRAGMENTS(DISPLAY_FRAGMENTS_STRUCT_ID, DISPLAY_FRAGMENTS_VARBIT_ID),
    TOOLTIPS_FOR_BUFFS(TOOLTIPS_FOR_BUFFS_STRUCT_ID, TOOLTIPS_FOR_BUFFS_VARBIT_ID),
    TELEPORT_BLOCK_DURATION(TELEPORT_BLOCK_DURATION_STRUCT_ID, TELEPORT_BLOCK_DURATION_VARBIT_ID),
    CHARGE_BUFF_DURATION(CHARGE_BUFF_DURATION_STRUCT_ID, CHARGE_BUFF_DURATION_VARBIT_ID),
    GODWARS_ALTAR_COOLDOWN(GODWARS_ALTAR_COOLDOWN_STRUCT_ID, GODWARS_ALTAR_COOLDOWN_VARBIT_ID),
    DRAGONFIRE_SHIELD_COOLDOWN(DRAGONFIRE_SHIELD_COOLDOWN_STRUCT_ID, DRAGONFIRE_SHIELD_COOLDOWN_VARBIT_ID),
    IMBUED_HEART_COOLDOWN(IMBUED_HEART_COOLDOWN_STRUCT_ID, IMBUED_HEART_COOLDOWN_VARBIT_ID),
    VENGEANCE_COOLDOWN(VENGEANCE_COOLDOWN_STRUCT_ID, VENGEANCE_COOLDOWN_VARBIT_ID),
    VENGEANCE_ACTIVE(VENGEANCE_ACTIVE_STRUCT_ID, VENGEANCE_ACTIVE_VARBIT_ID),
    STAMINA_DURATION(STAMINA_DURATION_STRUCT_ID, STAMINA_DURATION_VARBIT_ID),
    PRAYER_ENHANCE_DURATION(PRAYER_ENHANCE_DURATION_STRUCT_ID, PRAYER_ENHANCE_DURATION_VARBIT_ID),
    OVERLOAD_DURATION(OVERLOAD_DURATION_STRUCT_ID, OVERLOAD_DURATION_VARBIT_ID),
    MAGIC_IMBUE_DURATION(MAGIC_IMBUE_DURATION_STRUCT_ID, MAGIC_IMBUE_DURATION_VARBIT_ID),
    ABYSSAL_SIRE_STUN_DURATION(ABYSSAL_SIRE_STUN_DURATION_STRUCT_ID, ABYSSAL_SIRE_STUN_DURATION_VARBIT_ID),
    FREEZEENTANGLED_DURATION(FREEZEENTANGLED_DURATION_STRUCT_ID, FREEZEENTANGLED_DURATION_VARBIT_ID),
    STAFF_OF_THE_DEAD_SPECIAL_DURATION(STAFF_OF_THE_DEAD_SPECIAL_DURATION_STRUCT_ID,
            STAFF_OF_THE_DEAD_SPECIAL_DURATION_VARBIT_ID),
    DIVINE_POTION_DURATIONS(DIVINE_POTION_DURATIONS_STRUCT_ID, DIVINE_POTION_DURATIONS_VARBIT_ID),
    ANTIFIRE_POTION_DURATIONS(ANTIFIRE_POTION_DURATIONS_STRUCT_ID, ANTIFIRE_POTION_DURATIONS_VARBIT_ID),
    ANTIVENOM_AND_POISON_POTION_DURATIONS(ANTIVENOM_AND_POISON_POTION_DURATIONS_STRUCT_ID,
            ANTIVENOM_AND_POISON_POTION_DURATIONS_VARBIT_ID),
    ALWAYS_ON_TOP(ALWAYS_ON_TOP_STRUCT_ID, ALWAYS_ON_TOP_VARBIT_ID),
    DISPLAY_MODIFIED_STATS_OVERLAY(DISPLAY_MODIFIED_STATS_OVERLAY_STRUCT_ID, DISPLAY_MODIFIED_STATS_OVERLAY_VARBIT_ID),
    SHOW_TOOLTIPS_FOR_MODIFIED_STAT_OVERLAYS(SHOW_TOOLTIPS_FOR_MODIFIED_STAT_OVERLAYS_STRUCT_ID,
            SHOW_TOOLTIPS_FOR_MODIFIED_STAT_OVERLAYS_VARBIT_ID),
    DISPLAY_RELATIVE_STAT_VALUE(DISPLAY_RELATIVE_STAT_VALUE_STRUCT_ID, DISPLAY_RELATIVE_STAT_VALUE_VARBIT_ID),
    CORRUPTION_ACTIVE_AND_DURATION(CORRUPTION_ACTIVE_AND_DURATION_STRUCT_ID, CORRUPTION_ACTIVE_AND_DURATION_VARBIT_ID),
    MARK_OF_DARKNESS_ACTIVE(MARK_OF_DARKNESS_ACTIVE_STRUCT_ID, MARK_OF_DARKNESS_ACTIVE_VARBIT_ID),
    SHADOW_VEIL_ACTIVE_AND_DURATION(SHADOW_VEIL_ACTIVE_AND_DURATION_STRUCT_ID,
            SHADOW_VEIL_ACTIVE_AND_DURATION_VARBIT_ID),
    DEATH_CHARGE_ACTIVE_AND_DURATION(DEATH_CHARGE_ACTIVE_AND_DURATION_STRUCT_ID,
            DEATH_CHARGE_ACTIVE_AND_DURATION_VARBIT_ID),
    WARD_OF_ARCEUUS_ACTIVE_AND_DURATION(WARD_OF_ARCEUUS_ACTIVE_AND_DURATION_STRUCT_ID,
            WARD_OF_ARCEUUS_ACTIVE_AND_DURATION_VARBIT_ID),
    RESURRECTION_ACTIVE_AND_DURATION(RESURRECTION_ACTIVE_AND_DURATION_STRUCT_ID,
            RESURRECTION_ACTIVE_AND_DURATION_VARBIT_ID),
    COMBAT_ACHIEVEMENT_TASKS_FAILURE(COMBAT_ACHIEVEMENT_TASKS_FAILURE_STRUCT_ID,
            COMBAT_ACHIEVEMENT_TASKS_FAILURE_VARBIT_ID),
    COMBAT_ACHIEVEMENT_TASKS_COMPLETION_POPUP(COMBAT_ACHIEVEMENT_TASKS_COMPLETION_POPUP_STRUCT_ID,
            COMBAT_ACHIEVEMENT_TASKS_COMPLETION_POPUP_VARBIT_ID),
    COMBAT_ACHIEVEMENT_TASKS_REPEAT_COMPLETION(COMBAT_ACHIEVEMENT_TASKS_REPEAT_COMPLETION_STRUCT_ID,
            COMBAT_ACHIEVEMENT_TASKS_REPEAT_COMPLETION_VARBIT_ID),
    COMBAT_ACHIEVEMENT_TASKS_REPEAT_FAILURE(COMBAT_ACHIEVEMENT_TASKS_REPEAT_FAILURE_STRUCT_ID,
            COMBAT_ACHIEVEMENT_TASKS_REPEAT_FAILURE_VARBIT_ID),
    REMAINING_AMMO(REMAINING_AMMO_STRUCT_ID, REMAINING_AMMO_VARBIT_ID),
    HIGHLIGHT_HOVERED_TILE(HIGHLIGHT_HOVERED_TILE_STRUCT_ID, HIGHLIGHT_HOVERED_TILE_VARBIT_ID),
    HIGHLIGHT_CURRENT_TILE(HIGHLIGHT_CURRENT_TILE_STRUCT_ID, HIGHLIGHT_CURRENT_TILE_VARBIT_ID),
    HIGHLIGHT_DESTINATION_TILE(HIGHLIGHT_DESTINATION_TILE_STRUCT_ID, HIGHLIGHT_DESTINATION_TILE_VARBIT_ID),
    HIGHLIGHT_HOVERED_TILE_ALWAYS_ON_TOP(HIGHLIGHT_HOVERED_TILE_ALWAYS_ON_TOP_STRUCT_ID,
            HIGHLIGHT_HOVERED_TILE_ALWAYS_ON_TOP_VARBIT_ID),
    HIGHLIGHT_CURRENT_TILE_ALWAYS_ON_TOP(HIGHLIGHT_CURRENT_TILE_ALWAYS_ON_TOP_STRUCT_ID,
            HIGHLIGHT_CURRENT_TILE_ALWAYS_ON_TOP_VARBIT_ID),
    HIGHLIGHT_DESTINATION_TILE_ALWAYS_ON_TOP(HIGHLIGHT_DESTINATION_TILE_ALWAYS_ON_TOP_STRUCT_ID,
            HIGHLIGHT_DESTINATION_TILE_ALWAYS_ON_TOP_VARBIT_ID),
    ENABLE_MINIMAP_ZOOM(ENABLE_MINIMAP_ZOOM_STRUCT_ID, ENABLE_MINIMAP_ZOOM_VARBIT_ID),
    HIGHLIGHT_ENTITIES_ON_MOUSEOVER(HIGHLIGHT_ENTITIES_ON_MOUSEOVER_STRUCT_ID,
            HIGHLIGHT_ENTITIES_ON_MOUSEOVER_VARBIT_ID),
    HIGHLIGHT_AGILITY_SHORTCUTS_SHORTCUT_REQUIREMENTS(HIGHLIGHT_AGILITY_SHORTCUTS_SHORTCUT_REQUIREMENTS_STRUCT_ID,
            HIGHLIGHT_AGILITY_SHORTCUTS_SHORTCUT_REQUIREMENTS_VARBIT_ID),
    BIRD_NEST_NOTIFICATION(BIRD_NEST_NOTIFICATION_STRUCT_ID, BIRD_NEST_NOTIFICATION_VARBIT_ID),
    SLAYER_HELPER(SLAYER_HELPER_STRUCT_ID, SLAYER_HELPER_VARBIT_ID),
    IRON_LOOT_RESTRICTION_INDICATOR(IRON_LOOT_RESTRICTION_INDICATOR_STRUCT_ID,
            IRON_LOOT_RESTRICTION_INDICATOR_VARBIT_ID),
    IRON_LOOT_RESTRICTION_MESSAGES(IRON_LOOT_RESTRICTION_MESSAGES_STRUCT_ID, IRON_LOOT_RESTRICTION_MESSAGES_VARBIT_ID),
    HIGHLIGHT_ENTITIES_ON_TAP(HIGHLIGHT_ENTITIES_ON_TAP_STRUCT_ID, HIGHLIGHT_ENTITIES_ON_TAP_VARBIT_ID),
    VIBRATE_ON_INTERACTION(VIBRATE_ON_INTERACTION_STRUCT_ID, VIBRATE_ON_INTERACTION_VARBIT_ID),
    VIBRATE_WHEN_MINIMENU_OPENS(VIBRATE_WHEN_MINIMENU_OPENS_STRUCT_ID, VIBRATE_WHEN_MINIMENU_OPENS_VARBIT_ID),
    VIBRATE_ON_DRAG(VIBRATE_ON_DRAG_STRUCT_ID, VIBRATE_ON_DRAG_VARBIT_ID),
    VIBRATE_WHEN_HOVERING_OVER_MINIMENU_ENTRIES(VIBRATE_WHEN_HOVERING_OVER_MINIMENU_ENTRIES_STRUCT_ID,
            VIBRATE_WHEN_HOVERING_OVER_MINIMENU_ENTRIES_VARBIT_ID),
    PK_SKULL_PREVENTION(PK_SKULL_PREVENTION_STRUCT_ID, PK_SKULL_PREVENTION_VARBIT_ID, 1),
    ANTI_DRAG(ANTI_DRAG_STRUCT_ID, ANTI_DRAG_VARBIT_ID),
    EXAMINE_PRICE_INFO_GRAND_EXCHANGE(EXAMINE_PRICE_INFO_GRAND_EXCHANGE_STRUCT_ID,
            EXAMINE_PRICE_INFO_GRAND_EXCHANGE_VARBIT_ID),
    CHATBOX_MODE_SET_AUTOMATICALLY(CHATBOX_MODE_SET_AUTOMATICALLY_STRUCT_ID, CHATBOX_MODE_SET_AUTOMATICALLY_VARBIT_ID),
    EXAMINE_PRICE_INFO_ALCHEMY(EXAMINE_PRICE_INFO_ALCHEMY_STRUCT_ID, EXAMINE_PRICE_INFO_ALCHEMY_VARBIT_ID),
    LOGOUT_NOTIFIER(LOGOUT_NOTIFIER_STRUCT_ID, LOGOUT_NOTIFIER_VARBIT_ID),
    HIDE_UNAVAILABLE_QUESTS(HIDE_UNAVAILABLE_QUESTS_STRUCT_ID, HIDE_UNAVAILABLE_QUESTS_VARBIT_ID),
    POISON_DAMAGE(POISON_DAMAGE_STRUCT_ID, POISON_DAMAGE_VARBIT_ID),
    HIDE_QUESTS(HIDE_QUESTS_STRUCT_ID, HIDE_QUESTS_VARBIT_ID),
    HIDE_MINIQUESTS(HIDE_MINIQUESTS_STRUCT_ID, HIDE_MINIQUESTS_VARBIT_ID),
    HIDE_QUEST_LIST_HEADERS(HIDE_QUEST_LIST_HEADERS_STRUCT_ID, HIDE_QUEST_LIST_HEADERS_VARBIT_ID),
    SHOW_NUMBER_OF_OPTIONS_IN_MOUSEOVER_TOOLTIPS(SHOW_NUMBER_OF_OPTIONS_IN_MOUSEOVER_TOOLTIPS_STRUCT_ID,
            SHOW_NUMBER_OF_OPTIONS_IN_MOUSEOVER_TOOLTIPS_VARBIT_ID),
    DISABLE_QUEST_LIST_TEXT_SHADOWS(DISABLE_QUEST_LIST_TEXT_SHADOWS_STRUCT_ID,
            DISABLE_QUEST_LIST_TEXT_SHADOWS_VARBIT_ID),
    HIDE_COMPLETED_QUESTS(HIDE_COMPLETED_QUESTS_STRUCT_ID, HIDE_COMPLETED_QUESTS_VARBIT_ID),
    HIDE_QUESTS_IN_PROGRESS(HIDE_QUESTS_IN_PROGRESS_STRUCT_ID, HIDE_QUESTS_IN_PROGRESS_VARBIT_ID),
    HIDE_UNSTARTED_QUESTS(HIDE_UNSTARTED_QUESTS_STRUCT_ID, HIDE_UNSTARTED_QUESTS_VARBIT_ID),
    ACCEPT_TRADE_DELAY(ACCEPT_TRADE_DELAY_STRUCT_ID, ACCEPT_TRADE_DELAY_VARBIT_ID),
    XP_TRACKER(XP_TRACKER_STRUCT_ID, XP_TRACKER_VARBIT_ID),
    DESERT_HEAT_DAMAGE(DESERT_HEAT_DAMAGE_STRUCT_ID, DESERT_HEAT_DAMAGE_VARBIT_ID),
    SHOW_CONFIRMATION_WHEN_PAYING_FOR_ITEMS_FROM_GRAVESTONE(SHOW_CONFIRMATION_WHEN_PAYING_FOR_ITEMS_FROM_GRAVESTONE_STRUCT_ID, SHOW_CONFIRMATION_WHEN_PAYING_FOR_ITEMS_FROM_GRAVESTONE_VARBIT_ID),
    SHOW_ACTIVITY_ADVISER(SHOW_ACTIVITY_ADVISER_STRUCT_ID, SHOW_ACTIVITY_ADVISER_VARBIT_ID),
    ;

    private final int struct;
    private final int varbit;

    private final int defaultValue;

    ToggleSettingVarBits(int struct, int varbit, int defaultValue) {
        this.struct = struct;
        this.varbit = varbit;
        this.defaultValue = defaultValue;
    }

    ToggleSettingVarBits(int struct, int varbit) {
        this(struct, varbit, 0);
    }

    @Override
    public int getStruct() {
        return struct;
    }

    public int getVarbit() {
        return varbit;
    }

    public int getDefaultValue() {
        return defaultValue;
    }

    @Override
    public Integer get() {
        return getVarbit();
    }

    public static final ToggleSettingVarBits[] values = values();

}
