package com.zenyte.game.model.ui.testinterfaces;

import com.near_reality.game.content.middleman.MiddleManPlayerPromptKt;
import com.zenyte.game.GameInterface;
import com.zenyte.game.content.consumables.Consumable;
import com.zenyte.game.content.consumables.drinks.BarbarianMix;
import com.zenyte.game.content.consumables.drinks.Potion;
import com.zenyte.game.content.consumables.edibles.Food;
import com.zenyte.game.content.minigame.duelarena.Duel;
import com.zenyte.game.content.minigame.duelarena.DuelSetting;
import com.zenyte.game.content.skills.magic.Magic;
import com.zenyte.game.content.skills.magic.SpellState;
import com.zenyte.game.content.skills.magic.Spellbook;
import com.zenyte.game.content.skills.magic.spells.lunar.CureMe;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.model.ui.InterfacePosition;
import com.zenyte.game.world.entity.Toxins;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.Setting;
import com.zenyte.game.world.entity.player.container.impl.Inventory;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.region.area.wilderness.WildernessArea;
import mgi.types.config.items.ItemDefinitions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.zenyte.game.GameInterface.WORLD_MAP;
import static com.zenyte.game.world.entity.Toxins.ToxinType.*;

/**
 * <AUTHOR> | 28-10-2018 | 16:27
 */
public class OrbsInterface extends Interface {
    private static final Logger log = LoggerFactory.getLogger(OrbsInterface.class);

    @Override
    protected void attach() {
        put(3, "Adventurers Log");
        put(5, "Experience Tracker");
        put(8, "Cure Toxins");
        put(19, "Prayer");
        put(27, "Run");
        put(35, "Toggle special");
        put(45, "PvP Info");
        put(53, "View World Map");
    }

    @Override
    public void open(Player player) {
        if (player.isOnMobile() && player.getBooleanSetting(Setting.MINIMIZE_MINIMAP)) {
            GameInterface.MINIMIZED_ORBS.open(player);
            return;
        }
        player.getInterfaceHandler().sendInterface(getInterface());
    }

    @Override
    protected void build() {
        bind("Adventurers Log", (player, slotId, itemId, option) -> {
            player.sendMessage("The Adventurer's Log is currently under development.");
        });

        bind("Toggle special", (player, slotId, itemId, option) -> {
            if (player.isLocked()) {
                return;
            }
            final Duel duel = player.getDuel();
            if (duel != null && duel.hasRule(DuelSetting.NO_SPECIAL_ATTACK) && duel.inDuel()) {
                player.sendMessage("Use of special attacks has been turned off for this duel.");
                return;
            }
            player.getCombatDefinitions().setSpecial(!player.getCombatDefinitions().isUsingSpecial(), false);
        });

        bind("Experience Tracker", (player, slotId, itemId, option) -> {
            if (option == 1) {
                player.getSettings().toggleSetting(Setting.EXPERIENCE_TRACKER);
            } else if (option == 2) {
                if (player.isLocked() || player.getInterfaceHandler().containsInterface(InterfacePosition.CENTRAL)) {
                    player.sendMessage("You can't configure your XP drops at the moment.");
                    return;
                }
                GameInterface.EXPERIENCE_TRACKER.open(player);
            } else if (option == 3) {
                player.getDialogueManager().start(new Dialogue(player) {
                    @Override
                    public void buildDialogue() {
                        options(TITLE,
                                new DialogueOption("Show x" + player.getSkillingXPRate() + " experience drops", () -> {
                                    player.setXpDropsMultiplied(false);
                                    player.setXPDropsWildyOnly(false);
                                    player.getVarManager().sendVar(3504, 1);
                                }),
                                new DialogueOption("Show x1 experience drops in Wilderness", () -> {
                                    player.setXpDropsMultiplied(true);
                                    player.setXPDropsWildyOnly(true);
                                    player.getVarManager().sendVar(3504, WildernessArea.isWithinWilderness(player.getX(), player.getY()) ? player.getSkillingXPRate() : 1);
                                }),
                                new DialogueOption("Show x1 experience drops everywhere", () -> {
                                    player.setXpDropsMultiplied(true);
                                    player.setXPDropsWildyOnly(false);
                                    player.getVarManager().sendVar(3504, player.getSkillingXPRate());
                                }),
                                new DialogueOption("Cancel"));
                    }
                });
            }
        });

        bind("Cure Toxins", (player, slotId, itemId, option) -> {
            if (player.isLocked() || player.getInterfaceHandler().containsInterface(InterfacePosition.CENTRAL)) {
                player.sendMessage("You can't do that right now.");
                return;
            }
            final Toxins toxins = player.getToxins();
            final Toxins.ToxinType type = player.getVarManager().getBitValue(10151) > 0 ? PARASITE : toxins.isVenomed() ? VENOM : toxins.isPoisoned() ? POISON : toxins.isDiseased() ? DISEASE : null;
            if (type == null) {
                return;
            }
            final Inventory inventory = player.getInventory();
            for (int i = 0; i < 28; i++) {
                final Item item = inventory.getItem(i);
                if (item == null) {
                    continue;
                }
                final ItemDefinitions definitions = item.getDefinitions();
                if (definitions == null || definitions.isNoted()) {
                    continue;
                }
                switch (type) {
                    case PARASITE: {
                        final Consumable consumable = Consumable.consumables.get(item.getId());
                        if (consumable == Potion.RELICYMS_BALM || consumable == BarbarianMix.RELICYMS_MIX || consumable == Potion.SANFEW_SERUM) {
                            consumable.consume(player, item, i);
                            return;
                        }
                        continue;
                    }
                    case DISEASE: {
                        final String name = item.getName().toLowerCase();
                        if (name.contains("relicym's")) {
                            final Consumable consumable = Consumable.consumables.get(item.getId());
                            if (consumable != null) {
                                if (consumable == Potion.RELICYMS_BALM || consumable == BarbarianMix.RELICYMS_MIX) {
                                    consumable.consume(player, item, i);
                                    return;
                                }
                            }
                        }
                        continue;
                    }
                    case POISON: {
                        final Consumable consumable = Consumable.consumables.get(item.getId());
                        if (consumable == Potion.ANTIPOISON || consumable == Potion.SUPERANTIPOISON || consumable == Potion.ANTIDOTE_PLUS || consumable == Potion.ANTIDOTE_PLUS_PLUS || consumable == Food.STRANGE_FRUIT || consumable == Potion.GUTHIX_REST || consumable == Potion.SANFEW_SERUM || consumable == BarbarianMix.ANTIPOISON_MIX || consumable == BarbarianMix.ANTIPOISON_SUPERMIX || consumable == BarbarianMix.ANTIDOTE_PLUS_MIX) {
                            consumable.consume(player, item, i);
                            return;
                        }
                        continue;
                    }
                    case VENOM: {
                        final Consumable consumable = Consumable.consumables.get(item.getId());
                        if (consumable == Potion.ANTIPOISON || consumable == Potion.SUPERANTIPOISON || consumable == Potion.ANTIDOTE_PLUS_PLUS || consumable == BarbarianMix.ANTIPOISON_MIX || consumable == BarbarianMix.ANTIPOISON_SUPERMIX || consumable == Potion.ANTI_VENOM || consumable == Potion.ANTI_VENOM_PLUS) {
                            consumable.consume(player, item, i);
                            return;
                        }
                    }
                }
            }
            if (type == POISON || type == VENOM) {
                final CureMe spell = Magic.getSpell(Spellbook.LUNAR, "cure me", CureMe.class);
                if (spell != null && spell.canCast(player)) {
                    if (!spell.canUse(player)) {
                        return;
                    }
                    final SpellState state = new SpellState(player, spell.getLevel(), spell.getRunes());
                    if (state.check(false)) {
                        try {
                            spell.execute(player, 1, "Cast");
                        } catch (Exception e) {
                            log.error("", e);
                        }
                        return;
                    }
                }
            }
            player.sendMessage("You haven't got any potions to cure the " + type.toString().toLowerCase() + ".");
        });

        bind("Prayer", (player, slotId, itemId, option) -> {
            if (option == 1) {
                player.getPrayerManager().toggleQuickPrayers();
            } else if (option == 2) {
                if (player.isLocked() || player.getInterfaceHandler().containsInterface(InterfacePosition.CENTRAL)) {
                    player.sendMessage("You can't set up your prayers at the moment.");
                    return;
                }
                player.getPrayerManager().openQuickPrayers();
            }
        });

        bind("Run", player -> player.setRun(!player.isRun()));

        bind("View World Map", (player, slotId, itemId, option) -> {
            if (player.isLocked() || (option == 3 && player.getInterfaceHandler().containsInterface(InterfacePosition.CENTRAL))) {
                player.sendMessage("You can't do that right now.");
                return;
            }
            if (!player.getWorldMap().isVisible()) {
                if (option == 3) {
                    if (player.isUnderCombat()) {
                        player.sendMessage("You cannot open full-screen world map while under attack.");
                        return;
                    }
                }
                player.getWorldMap().setFullScreen(option == 3);
                WORLD_MAP.open(player);
            } else {
                player.getWorldMap().close();
            }
        });

        bind("PvP Info", (player, slotId, itemId, option) -> {
            if (player.isLocked() || player.getInterfaceHandler().containsInterface(InterfacePosition.CENTRAL)) {
                player.sendMessage("You can't do that right now.");
                return;
            }

            if (option == 1) {
                player.getDialogueManager().start(new Dialogue(player, 6773) {
                    @Override
                    public void buildDialogue() {
                        npc("Greetings, adventurer! I'm here to explain how PvP works on Exiles.");
                        player("I'd like to know more about the wilderness and PvP.");
                        npc("Excellent! First, you should know that the wilderness on Exiles works differently than you might expect.");
                        npc("By default, PvP is completely disabled in the wilderness. You can explore safely without fear of other players.");
                        player("So I can't be attacked by other players at all?");
                        npc("That's correct - unless you choose to enable PvP by skulling yourself. Let me explain how this works.");
                        npc("Certain wilderness activities require you to skull up, which enables PvP. These include the Wilderness Resource Area, Chaos Altar, and Revenant Caves.");
                        player("What happens when I'm skulled?");
                        npc("When you have a skull, you can be attacked by other skulled players. However, there's a significant benefit!");
                        npc("Skulled players receive a 50% increase to all PvE Wilderness drop rates. Risk versus reward!");
                        player("So let me get this straight...");
                        npc("Here's the simple rule: No skull equals PvP disabled and safe exploration. Skulled equals PvP enabled with better rewards!");
                        npc("You have full control over when you want to engage in PvP. The choice is always yours, adventurer.");
                        npc("Also, fair warning! The skull will last until you remove it at home by right-clicking on the Ornate Pool and choosing Remove-Skull.");
                        player("Thank you for the explanation!");
                        npc("You're welcome! Stay safe out there, and remember - skull up only when you're ready for the challenge!");
                        options("Would you like to go ahead and skull?",
                                "<col=ff0000>Yes, skull me up!</col>",
                                "No, not now.")
                                .onOptionOne(() -> player.getVariables().setSkull(true));
                    }
                });
            } else if (option == 2) {
                if (player.getBooleanAttribute("NO_PVP_WARNING")) {
                    player.getVariables().setSkull(true);
                    player.sendMessage("You are now skulled. PvP enabled!");
                    return;
                }
                player.getDialogueManager().start(new Dialogue(player) {
                    @Override
                    public void buildDialogue() {
                        plain("Your skull will only be removed by death or right clicking the Ornate Fountain at home and choosing Remove-Skull.");
                        options("Are you sure you want to enable Wildy PvP?",
                                "<col=ff0000>Yes, skull me up!</col>",
                                "<col=ff0000>Yes, and don't warn me again.</col>",
                                "No, I've changed my mind.")
                                .onOptionOne(() -> player.getVariables().setSkull(true))
                                .onOptionTwo(() -> {
                                    player.toggleBooleanAttribute("NO_PVP_WARNING");
                                    player.getVariables().setSkull(true);
                                });
                    }
                });
            }
        });
    }

    @Override
    public GameInterface getInterface() {
        return GameInterface.ORBS;
    }
}
