package com.zenyte.game.model.ui.testinterfaces;

import com.zenyte.game.GameInterface;
import com.zenyte.game.content.essence.tasks.type.CombatTask;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.util.AccessMask;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Analytics;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.content.essence.tasks.TaskProgress;
import com.zenyte.game.content.essence.tasks.TaskManager;
import com.zenyte.game.content.essence.tasks.type.Task;
import com.zenyte.game.content.essence.tasks.type.SkillingTask;
import com.zenyte.game.content.essence.tasks.reward.TaskReward;
import com.zenyte.game.content.essence.tasks.reward.RewardType;
import com.zenyte.game.content.essence.tasks.reward.impl.ExperienceReward;
import com.zenyte.game.content.essence.tasks.reward.impl.ItemReward;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.utils.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR> | 06/05/2019 | 16:38
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>
 * <AUTHOR> (Discord: astra4) - Refactored
 */
public class UtilityTaskOverviewInterface extends Interface {
    private static final Logger log = LoggerFactory.getLogger(UtilityTaskOverviewInterface.class);

    @Override
    protected void attach() {
        put(10, "0");
        put(11, "1");
        put(12, "2");
        put(13, "3");
        put(14, "4");
        put(15, "5");
        put(16, "Essence Overview");
        put(17, "Utility Perk Shop");
    }

    @Override
    public void open(Player player) {
        Analytics.flagInteraction(player, Analytics.InteractionType.DAILY_CHALLENGES);
        final TaskManager manager = player.getTaskManager();
        final Map<Task, TaskProgress> challenges = manager.getTaskProgression();
        player.getInterfaceHandler().sendInterface(this);
        if (challenges.isEmpty()) {
            player.getPacketDispatcher().sendClientScript(12609, "");
            return;
        }

        int index = 0;
        StringBuilder sb = new StringBuilder();
        for (final Task challenge : challenges.keySet()) {
            transmitDailyChallenge(player, challenge, index, sb);
            index++;
        }

        log.info("StringBuilder Task Overview: {}", sb);

        player.getPacketDispatcher().sendClientScript(12609, sb.toString());
    }

    private void transmitDailyChallenge(final Player player, final Task challenge, final int index, final StringBuilder sb) {
        final TaskManager manager = player.getTaskManager();
        final TaskProgress progression = manager.getProgress(challenge);
        if (progression == null) {
            return;
        }
        final int progress = progression.getProgress();
        final int extra = challenge instanceof SkillingTask ? ((SkillingTask) challenge).getSkill() : 0;
        final StringBuilder builder = new StringBuilder();
        final TaskReward[] rewards = challenge.getRewards();
        for (final TaskReward reward : rewards) {
            if (reward.getType().equals(RewardType.ITEM)) {
                final ItemReward itemReward = (ItemReward) reward;
                builder.append(itemReward.getItem().getId()).append("x");
                builder.append(itemReward.getItem().getAmount()).append(" ");
            } else {
                final ExperienceReward experienceReward = (ExperienceReward) reward;
                builder.append(TextUtils.formatCurrency(experienceReward.getExperience(player) * player.getExperienceRate(experienceReward.getSkill()))).append(" ");
                builder.append(SkillConstants.SKILLS[experienceReward.getSkill()]).append(" XP");
            }
        }

        final int coinReward = challenge.getDifficulty().getCoins();
        if(challenge instanceof CombatTask) {
            builder.append(Utils.formatNumWDot(coinReward)).append(" Coins");
        }

        final int utilityEssenceReward = challenge.getDifficulty().getUtilityEssence();
        builder.append(" & ").append(Utils.formatNumberWithCommas(utilityEssenceReward)).append(" Utility Essence");

        sb.append(challenge.getName());
        sb.append("|");
        sb.append(SkillConstants.SKILLS_ICONS[extra]);
        sb.append("|");
        sb.append(20);
        sb.append("|");
        sb.append(32);
        sb.append("|");
        sb.append(progress);
        sb.append("|");
        sb.append(challenge.getLength());
        sb.append("|");
        if (challenge instanceof SkillingTask) {
            sb.append("Skilling");
        } else {
            sb.append("Combat");
        }
        sb.append("|");
        sb.append(challenge.getDifficulty());
        sb.append("|");
        sb.append(builder);
        sb.append("|");
        player.getPacketDispatcher().sendComponentSettings(getInterface(), getComponent(Integer.toString(index)), 0, 10, AccessMask.CLICK_OP1, AccessMask.CLICK_OP2);
    }

    @Override
    protected void build() {
        for (int i = 0; i <= 5; i++) {
            final int index = i; // capture for lambda
            bind(String.valueOf(index), (player, slotId, itemId, option) -> {
                switch (option) {
                    case 1 -> claim(player, index);
                    case 2 -> reject(player, index);
                }
            });
        }
        bind("Essence Overview", GameInterface.PERK_OVERVIEW::open);
        bind("Utility Perk Shop", GameInterface.UTILITY_PERK_SHOP::open);
    }

    private void claim(Player player, int index) {
        final TaskManager manager = player.getTaskManager();
        final Task challenge = manager.getChallenge(index);
        if (manager.claim(challenge)) {
            close(player);
            open(player);
        }
    }

    private void reject(Player player, int index) {
        final TaskManager manager = player.getTaskManager();
        final Task challenge = manager.getChallenge(index);
        player.getDialogueManager().start(new Dialogue(player) {
            @Override
            public void buildDialogue() {
                options("Reject task: " + challenge.getName() + "?", "Yes, reject task", "No")
                        .onOptionOne(() -> {
                            manager.reject(challenge);
                            close(player);
                            open(player);
                        })
                        .onOptionTwo(() -> {
                            close(player);
                            open(player);
                        });
            }
        });

    }

    @Override
    public GameInterface getInterface() {
        return GameInterface.UTILITY_TASKS;
    }
}
