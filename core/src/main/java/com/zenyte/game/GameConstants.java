package com.zenyte.game;

import com.zenyte.ContentConstants;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.near_reality.game.world.info.WorldProfile;
import com.zenyte.utils.TimeUnit;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.Set;

/**
 * <AUTHOR> | 5. march 2018 : 17:05.26
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server
 *      profile</a>}
 * @see <a href="https://rune-status.net/members/kris.354/">Rune-Status
 *      profile</a>}
 */
public class GameConstants {

	/**
	 * The current cache revision.
	 */
	public static final int currentYear = 2025; //Used for the adventure calendar - Event occurs from Nov 30 - Dec 25 each year.
	public static final double REVISION = 225;
	public static final int CLIENT_VERSION = 3;
	public static final int WORLD_CYCLE_TIME = 600;
	public static final int LOGIN_PORT = 43596;
	public static final boolean DEV_DEBUG = false;
	public static final Location REGISTRATION_LOCATION = new Location(3074, 3504, 0);
	public static final String SERVER_NAME = ContentConstants.SERVER_NAME;
	public static final String SERVER_CHANNEL_NAME = "help";
	public static final String SERVER_WEBSITE_URL = "https://exiles-ps.com";
	public static final String SERVER_ACCOUNT_URL = SERVER_WEBSITE_URL + "/account";
	public static final String SERVER_RULES_URL = SERVER_WEBSITE_URL + "/rules";
	public static final String DISCORD_INVITE = "https://discord.gg/3yt9kXRdax";

	/**
	 * The instance world profile.
	 */
	public static WorldProfile WORLD_PROFILE;

	public static boolean CYCLE_DEBUG = false;

	public static boolean CHECK_HUNTER_TRAPS_QUANTITY = true;

	public static boolean WHITELISTING = true;
	public static boolean DUEL_ARENA = true;
	public static boolean GROTESQUE_GUARDIANS = true;
	public static boolean PURGING_CHUNKS = true;

	public static final Set<String> whitelistedUsernames = new ObjectOpenHashSet<>();

	public static double defenceMultiplier = 0.825;

	public static int randomEvent = (int) TimeUnit.HOURS.toTicks(5);

	public static boolean CHAMBERS_OF_XERIC = true;
	public static boolean ALCHEMICAL_HYDRA = true;

	//!Case sensitive usernames
	public static final String[] owners = new String[] {
			"crystalline", "ryan", "tyler", "sal" 	};

	static {
		whitelistedUsernames.addAll(Arrays.asList(owners));
	}

	public static boolean isOwner(final Player player) {
		return ArrayUtils.contains(owners, player.getUsername().toLowerCase());
	}

	public static final float TICK = WORLD_CYCLE_TIME;

	public static final float CLIENT_CYCLE = 20;

	public static final float CYCLES_PER_TICK = TICK / CLIENT_CYCLE;

	public static boolean BOOSTED_XP = false;
	public static int BOOSTED_XP_MODIFIER = 150;

	public static boolean BOOSTED_SKILLING_PETS = true;
	public static final double BOOSTED_SKILLING_PET_RATE = 0.25; // this is a 15% boost

	public static boolean BOOSTED_BOSS_PETS = false;
	public static final double BOOSTED_BOSS_PET_RATE = 0.25; // this is a 15% boost

	public static final String UPDATE_LOG_URL = "https://discord.gg/3yt9kXRdax";

	/**
	 * Item ids added to this list are restricted from trade, selling to shops, and posting on the GE. It is primarily used as an economy control measure
	 */
	public static final IntArrayList RESTRICTED_TRADE_ITEMS = new IntArrayList();
}
