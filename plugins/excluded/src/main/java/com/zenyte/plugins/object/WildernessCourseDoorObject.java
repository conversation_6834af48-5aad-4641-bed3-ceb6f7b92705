package com.zenyte.plugins.object;


import com.zenyte.game.content.skills.agility.WildernessAgilityLapTracker;
import com.zenyte.game.content.skills.agility.wildernesscourse.WildernessCourse;
import com.zenyte.game.task.WorldTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.RenderAnimation;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.object.*;
import com.zenyte.game.world.region.area.wilderness.WildernessArea;

/**
 * <AUTHOR> (Discord: imslickk) - 7/25/25
 */
public final class WildernessCourseDoorObject implements ObjectAction {

    private void wildernessCourseChecks(Player player, WorldObject object) {
        if (WildernessArea.isWithinWilderness(player) && !player.getVariables().isSkulled()) {
            player.getDialogueManager().start(new Dialogue(player) {
                @Override
                public void buildDialogue() {
                    plain("To enter this course, you must skull - " + Colour.RED.wrap("This enables PvP") + ". Your skull will only be removed by death or right clicking the Ornate Fountain at home and choosing Remove-Skull.<br> Are you sure you wish to skull?");
                    options("Enable PvP?", new Dialogue.DialogueOption("Yes.", () -> {
                        player.getVariables().setSkull(true);
                        player.sendMessage(Colour.RED.wrap("You have now been SKULLED from entering the Wilderness Agility Course. PvP has been ENABLED."));
                        openDoor(player, object);
                    }), new Dialogue.DialogueOption("No."));
                }
            });
        } else {
            openDoor(player, object);
        }
    }

    private void openDoor(Player player, WorldObject object) {
        WildernessAgilityLapTracker.resetLapCount(player);
        final WorldObject door = Door.handleGraphicalDoor(object, null);
        player.setRunSilent(true);
        player.lock();
        player.freeze(16, 16);
        player.addWalkSteps(2998, 3931, -1, false);
        player.getAppearance().setRenderAnimation(new RenderAnimation(RenderAnimation.STAND, 762, RenderAnimation.WALK));
        WorldTasksManager.schedule(new WorldTask() {

            int ticks;

            DoubleDoor gate;

            @Override
            public void run() {
                switch(ticks++) {
                    case 1:
                        Door.handleGraphicalDoor(door, object);
                        break;
                    case 13:
                        player.getAppearance().resetRenderAnimation();
                        gate = DoubleDoor.handleGraphicalDoubleDoor(player, new WorldObject(23552, 0, 3, new Location(2998, 3931, 0)), null);
                        break;
                    case 15:
                        player.setRunSilent(false);
                        player.unlock();
                        DoubleDoor.handleGraphicalDoubleDoor(player, object, gate);
                        stop();
                        break;
                }
            }
        }, 0, 0);
    }


    @Override
    public void handleObjectAction(final Player player, final WorldObject object, final String name, final int optionId, final String option) {
        wildernessCourseChecks(player, object);
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { ObjectId.DOOR_23555 };
    }
}
