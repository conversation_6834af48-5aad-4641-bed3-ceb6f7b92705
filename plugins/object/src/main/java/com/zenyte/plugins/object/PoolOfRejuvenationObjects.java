package com.zenyte.plugins.object;

import com.near_reality.game.item.CustomObjectId;
import com.zenyte.game.content.consumables.Consumable;
import com.zenyte.game.content.consumables.ConsumableEffects;
import com.zenyte.game.world.entity.masks.Graphics;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.entity.player.privilege.MemberRank;
import com.zenyte.game.world.entity.player.var.VarCollection;
import com.zenyte.game.world.entity.player.variables.TickVariable;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.plugins.dialogue.PlainChat;
import com.zenyte.utils.TimeUnit;
import org.jetbrains.annotations.NotNull;

public class PoolOfRejuvenationObjects implements ObjectAction {

    private static final Graphics HEAL_GFX = new Graphics(1177);

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        if (option.equals("Drink")) {
            MemberRank memberRank = player.getMemberRank();
            drink(player, memberRank.equalToOrGreaterThan(MemberRank.REGULAR));
        } else if (option.equalsIgnoreCase("Remove-skull")) {
            if (player.getVariables().getTime(TickVariable.SKULL) > TimeUnit.MINUTES.toTicks(20)) {
                player.getDialogueManager().start(new PlainChat(player, "You are currently under the effects of a permanent skull and cannot be unskulled."));
                return;
            }
            player.blockIncomingHits();
            player.getVariables().removeSkull();
            player.sendFilteredMessage("Your skull has been removed.");
        } else if (option.equalsIgnoreCase("Skull")) {
            if (player.getBooleanAttribute("NO_PVP_WARNING")) {
                player.getVariables().setSkull(true);
                player.sendMessage("You are now skulled. PvP enabled!");
                return;
            }
            promptSkull(player);
        }

        if(object.getId() == CustomObjectId.OVERLOAD_POOL_OF_REJUVENATION && option.equalsIgnoreCase("Drink")) {
            if(!player.getMemberRank().equalToOrGreaterThan(MemberRank.DIVINE)) {
                player.sendMessage("Only Divine donators can use this.");
                return;
            }
            if(player.getVariables().getTime(TickVariable.OVERLOAD) > 0) {
                player.sendMessage("You must wait until your last overload effect is over.");
                return;
            }
            ConsumableEffects.damagePlayer(player, 5);
            player.getVariables().schedule(500, TickVariable.OVERLOAD);
            player.getVariables().setOverloadType((short) 3);
            player.getVarManager().sendBit(VarCollection.OVERLOAD_REFRESHES_REMAINING.getId(), 21);
            ConsumableEffects.applyOverload(player);
        }

        if(object.getId() == CustomObjectId.DIVINE_POOL_OF_REJUVENATION && option.equalsIgnoreCase("Drink")) {
            if(!player.getMemberRank().equalToOrGreaterThan(MemberRank.LEGENDARY)) {
                player.sendMessage("Only Legendary donators+ can use this.");
                return;
            }

            if(player.getAttributes().containsKey("DIVINE_POTION") && ((double) player.getAttributes().get("DIVINE_POTION")) > System.currentTimeMillis()) {
                player.sendMessage("You have to wait 5 minutes before using this again.");
                return;
            }
            new Consumable.Boost(SkillConstants.ATTACK, 0.15F, 5).apply(player);
            new Consumable.Boost(SkillConstants.DEFENCE, 0.15F, 5).apply(player);
            new Consumable.Boost(SkillConstants.STRENGTH, 0.15F, 5).apply(player);
            player.getVariables().schedule(500, TickVariable.DIVINE_SUPER_COMBAT_POTION);
            player.getAttributes().put("DIVINE_POTION", ((double)(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5))));
        }
    }

    public static void promptSkull(@NotNull final Player player) {
        player.getDialogueManager().finish();
        player.getDialogueManager().start(new Dialogue(player) {
            @Override
            public void buildDialogue() {
                options("Enable PvP in Wildy?",
                        new DialogueOption("Yes, skull me.",
                                () -> player.getVariables().setSkull(true)),
                        new DialogueOption("Yes, and don't warn me again.",
                                () -> {
                                    player.toggleBooleanAttribute("NO_PVP_WARNING");
                                    player.getVariables().setSkull(true);
                                }),
                        new DialogueOption("No, don't skull me."));
            }
        });
    }

    private void drink(Player player, boolean specialEnergy) {
        String appendMessage = "";

        player.setGraphics(HEAL_GFX);

        if (specialEnergy) {
            long time = player.getNumericAttribute("box of restoration delay").longValue();
            if (time <= System.currentTimeMillis()) {
                player.getCombatDefinitions().setSpecialEnergy(100);
                player.addAttribute("box of restoration delay", System.currentTimeMillis() + (TimeUnit.SECONDS.toMillis(getUsageCooldownDurationInSeconds(player))));
                appendMessage = ("As a donator, your special attack energy was restored!");
            } else {
                int totalSeconds = (int) TimeUnit.MILLISECONDS.toSeconds(time - System.currentTimeMillis());
                int seconds = totalSeconds % 60;
                appendMessage = ("You can recharge special attack energy in " + (seconds + " seconds") + ".");
            }
        }

        player.getVariables().setRunEnergy(100);
        player.getPrayerManager().restorePrayerPoints(99);
        player.heal(99);
        for (int i = 0; i < 22; i++) {
            if (i == SkillConstants.HITPOINTS || i == SkillConstants.PRAYER)
                continue;
            if (player.getSkills().getLevel(i) < player.getSkills().getLevelForXp(i))
                player.getSkills().setLevel(i, player.getSkills().getLevelForXp(i));
        }
        player.getToxins().reset();

        player.sendMessage("You feel replenished. " + appendMessage);
    }

    private int getUsageCooldownDurationInSeconds(Player player) {
        MemberRank memberRank = player.getMemberRank();
        if (memberRank.equalToOrGreaterThan(MemberRank.DIVINE)) {
            return 0;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.MYTHIC)) {
            return 0;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.LEGENDARY)) {
            return 0;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.EPIC)) {
            return 30;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.EXTREME)) {
            return 60;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.SUPER)) {
            return 90;
        } else if (memberRank.equalToOrGreaterThan(MemberRank.REGULAR)) {
            return 120;
        }
        return 180;
    }

    @Override
    public Object[] getObjects() {
        return new Object[] {CustomObjectId.ORNATE_POOL_OF_REJUVENATION, CustomObjectId.OVERLOAD_POOL_OF_REJUVENATION, CustomObjectId.DIVINE_POOL_OF_REJUVENATION};
    }
}
