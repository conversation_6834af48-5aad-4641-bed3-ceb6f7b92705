
package com.near_reality.plugins.interfaces.death

import com.near_reality.game.content.shop.ShopCurrencyHandler
import com.near_reality.game.item.CustomItemId
import com.near_reality.game.world.entity.player.dailyRemainingTomes
import com.near_reality.scripts.interfaces.InterfaceScript
import com.near_reality.scripts.interfaces.NamedInterfaceHandler
import com.near_reality.tools.logging.GameLogMessage
import com.near_reality.tools.logging.GameLogger
import com.zenyte.game.GameInterface
import com.zenyte.game.content.quests.QuestManager
import com.zenyte.game.content.quests.quests.AdventurersPath
import com.zenyte.game.item.Item
import com.zenyte.game.model.HintArrow
import com.zenyte.game.model.shop.ShopCurrency
import com.zenyte.game.task.WorldTasksManager.schedule
import com.zenyte.game.world.World
import com.zenyte.game.world.entity.SoundEffect
import com.zenyte.game.world.entity.npc.NPC
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.Dialogue.DialogueOption
import com.zenyte.game.world.entity.player.dialogue.options
import com.zenyte.game.world.entity.player.dialogue.start
import java.util.function.Consumer
import kotlin.math.min

/**
 * <AUTHOR> | 14/06/2022
 */
class CombatEssenceExchangeInterface : InterfaceScript() {
    init {
        GameInterface.SACRIFICE_ESSENCE {
            val forceConfirmItems = mutableListOf(CustomItemId.DONATOR_BOND_10, CustomItemId.DONATOR_BOND_25, CustomItemId.DONATOR_BOND_50, CustomItemId.DONATOR_BOND_100)
            val itemComponent = "Item component"(8)
            val portalComponent = "Portal component"(7)
            "Select 1"(9) {
                player.setCurrentSelectedItemQuantity(1)
            }
            "Select 5"(10) {
                player.setCurrentSelectedItemQuantity(5)
            }
            "Select X"(11) {
                player.sendInputInt("How many would you like to sacrifice?") { value ->
                    player.setCurrentSelectedItemQuantity(value.coerceAtLeast(1))
                }
            }
            "Select All"(12) {
                player.setCurrentSelectedItemQuantity(Int.MAX_VALUE)
            }
            "Confirm".suspend(13) {
                val slot = player.getCurrentSelectedItemSlot()
                val itemInSlot = player.inventory.getItem(slot) ?: return@suspend
                val price = player.getCurrentSelectedItemSacrificePrice()
                var quantity = min(player.inventory.getAmountOf(itemInSlot.id), player.getCurrentSelectedItemQuantity())
                if(itemInSlot.id == 30215) {//tomes
                    quantity = min(quantity, player.dailyRemainingTomes)
                    if(quantity == 0) {
                        player.sendMessage("You have reached the maximum daily sacrifice cap of 10 exp tomes")
                        return@suspend
                    }
                }
                if(quantity > 1 || forceConfirmItems.contains(itemInSlot.id)) {
                    askMultiSale(player, itemComponent, portalComponent, itemInSlot, price, quantity)
                    return@suspend
                }
                val success = player.inventory.deleteItem(Item(itemInSlot.id, quantity))
                val successfulAmount = success.succeededAmount
                if(itemInSlot.id == 30215)
                    player.dailyRemainingTomes = player.dailyRemainingTomes - successfulAmount
                ShopCurrencyHandler.add(ShopCurrency.COMBAT_ESSENCE, player, (successfulAmount * price.toLong()).toInt())
                if (AdventurersPath.isOnObjectiveWithProgress(player, AdventurersPath.UNLOCK_MAGIC_COMBAT_SPEED_PERK, 0) && ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player) >= 100 && itemInSlot.id == 32363) {
                    QuestManager.updateQuest(player, AdventurersPath::class.java, "UNLOCK_MAGIC_COMBAT_SPEED_PERK")
                    World.findNPC(NpcId.FIGHTER_11681, player.location, 20)
                        .ifPresent(Consumer { npc: NPC? -> player.packetDispatcher.sendHintArrow(HintArrow(npc)) })
                    player.sendMessage("<col=ff0000>You have sacrificed your Combat Essence Vouchers. Use your Combat Essence to purchase the Magic I combat speed perk now.");
                }
                GameLogger.log {
                    GameLogMessage.RemnantExchange(username = player.username, item = Item(itemInSlot.id, successfulAmount), value = price)
                }
                player.resetSelectedItem(itemComponent.componentID, portalComponent.componentID)
                player.sendSound(1595)
                player.sendSound(SoundEffect(2115, 0, 10))
                player.packetDispatcher.sendComponentAnimation(`interface`, portalComponent.componentID, 8747)
            }
            opened {
                resetSelectedItem(itemComponent.componentID, portalComponent.componentID)
                packetDispatcher.sendComponentItem(id, itemComponent.componentID, 6512, 400)
                sendInterface()
                GameInterface.SACRIFICE_ESSENCE_INVENTORY.open(this)
            }
            "Essence Overview".suspend(15) {
                GameInterface.PERK_OVERVIEW.open(player)
            }
            "Combat Perk Shop".suspend(14) {
                GameInterface.COMBAT_PERK_SHOP.open(player)
            }
            "Exchange Guide Prices".suspend(16) {
                GameInterface.SACRIFICE_PRICE_GUIDE.open(player)
            }
        }
    }

    fun Player.resetSelectedItem(componentId: Int, portalComponent: Int) {
        varManager.sendVarInstant(19451, -1)
        varManager.sendVarInstant(19450, ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, this).toLong().coerceIn(0..Int.MAX_VALUE.toLong()).toInt())
        packetDispatcher.sendComponentItem(id, componentId, 6512, 400)
        packetDispatcher.sendComponentAnimation(id, portalComponent, 7301)
        varManager.sendVarInstant(19452, 0)
    }

    fun Player.getCurrentSelectedItemSlot(): Int {
        return varManager.getValue(19451)
    }

    fun Player.getCurrentSelectedItemQuantity(): Int {
        return varManager.getValue(19452)
    }

    fun Player.setCurrentSelectedItemQuantity(value: Int) {
        val slot = getCurrentSelectedItemSlot()
        val item = inventory.getItem(slot) ?: return
        val result = if (value == Int.MAX_VALUE) Int.MAX_VALUE else value.coerceAtMost(inventory.getAmountOf(item.id))
        varManager.sendVarInstant(19452, result)
    }

    fun Player.getCurrentSelectedItemSacrificePrice(): Int {
        return varManager.getValue(19453)
    }

    fun askMultiSale(
        player: Player,
        itemComponent: NamedInterfaceHandler,
        portalComponent: NamedInterfaceHandler,
        itemInSlot: Item,
        price: Int,
        quantity: Int
    ) {
        player.dialogueManager.start {
            options("Are you sure you want to sacrifice $quantity x ${itemInSlot.name}?") {
                options += DialogueOption("Yes.") {
                    val success = player.inventory.deleteItem(Item(itemInSlot.id, quantity))
                    val successfulAmount = success.succeededAmount
                    if(itemInSlot.id == 30215)
                        player.dailyRemainingTomes = player.dailyRemainingTomes - successfulAmount
                    ShopCurrencyHandler.add(ShopCurrency.COMBAT_ESSENCE, player, (successfulAmount * price.toLong()).toInt())
                    if (AdventurersPath.isOnObjectiveWithProgress(player, AdventurersPath.UNLOCK_MAGIC_COMBAT_SPEED_PERK, 0) && ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player) >= 100 && itemInSlot.id == 32363) {
                        World.findNPC(NpcId.FIGHTER_11681, player.location, 20)
                            .ifPresent(Consumer { npc: NPC? -> player.packetDispatcher.sendHintArrow(HintArrow(npc)) })
                        QuestManager.updateQuest(player, AdventurersPath::class.java, "UNLOCK_MAGIC_COMBAT_SPEED_PERK")
                        player.sendMessage("<col=ff0000>You have sacrificed your Combat Essence Vouchers. Use your Combat Essence to purchase the Magic I combat speed perk now.");
                    }
                    player.resetSelectedItem(itemComponent.componentID, portalComponent.componentID)
                    player.sendSound(1595)
                    player.sendSound(SoundEffect(2115, 0, 10))
                    player.packetDispatcher.sendComponentAnimation(`interface`, portalComponent.componentID, 8747)
                    schedule(0) {
                        player.sendInterface()
                    }
                }
                options += DialogueOption("No.")
            }
        }
    }

}
